#!/usr/bin/env python3
"""
Stripe Connect Configuration Test Script
Tests the complete Stripe Connect setup for Ravid Healthcare Platform
"""

import os
import sys
import django
import stripe
import requests
from django.conf import settings
from django.contrib.auth import get_user_model

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from billing.config import BillingConfig
from billing.services.user_transfer_service import UserTransferService
from billing.models.customer import UserPaymentProfile

User = get_user_model()

def print_header(title):
    """Print formatted test header"""
    print(f"\n{'='*60}")
    print(f"🧪 {title}")
    print(f"{'='*60}")

def print_test(test_name):
    """Print test name"""
    print(f"\n🔍 Testing: {test_name}")

def print_success(message):
    """Print success message"""
    print(f"✅ {message}")

def print_error(message):
    """Print error message"""
    print(f"❌ {message}")

def print_warning(message):
    """Print warning message"""
    print(f"⚠️  {message}")

def test_basic_stripe_connectivity():
    """Test 1: Basic Stripe API connectivity"""
    print_test("Basic Stripe API Connectivity")
    
    try:
        stripe_keys = BillingConfig.get_stripe_keys()
        
        if not stripe_keys['secret_key']:
            print_error("STRIPE_SECRET_KEY not configured")
            return False
        
        stripe.api_key = stripe_keys['secret_key']
        stripe.api_version = BillingConfig.STRIPE_API_VERSION
        
        # Test basic API call
        customers = stripe.Customer.list(limit=1)
        print_success("Basic Stripe API connectivity successful")
        print(f"   API Version: {BillingConfig.STRIPE_API_VERSION}")
        print(f"   Key Type: {'Test' if stripe_keys['secret_key'].startswith('sk_test_') else 'Live'}")
        return True
        
    except stripe.error.AuthenticationError as e:
        print_error(f"Stripe authentication failed: {str(e)}")
        return False
    except Exception as e:
        print_error(f"Stripe API error: {str(e)}")
        return False

def test_connect_client_id():
    """Test 2: Stripe Connect Client ID validation"""
    print_test("Stripe Connect Client ID Configuration")
    
    try:
        client_id = getattr(settings, 'STRIPE_CONNECT_CLIENT_ID', '')
        
        if not client_id:
            print_error("STRIPE_CONNECT_CLIENT_ID not configured")
            return False
        
        if not client_id.startswith('ca_'):
            print_error(f"Invalid Connect Client ID format: {client_id[:10]}...")
            return False
        
        print_success("Connect Client ID properly configured")
        print(f"   Client ID: {client_id[:15]}...")
        return True
        
    except Exception as e:
        print_error(f"Error checking Connect Client ID: {str(e)}")
        return False

def test_connect_webhook_secret():
    """Test 3: Stripe Connect Webhook Secret validation"""
    print_test("Stripe Connect Webhook Secret Configuration")
    
    try:
        webhook_secret = getattr(settings, 'STRIPE_CONNECT_WEBHOOK_SECRET', '')
        
        if not webhook_secret:
            print_warning("STRIPE_CONNECT_WEBHOOK_SECRET not configured yet")
            print("   This is needed for webhook event processing")
            return False
        
        if not webhook_secret.startswith('whsec_'):
            print_error(f"Invalid webhook secret format: {webhook_secret[:10]}...")
            return False
        
        print_success("Connect Webhook Secret properly configured")
        print(f"   Secret: {webhook_secret[:15]}...")
        return True
        
    except Exception as e:
        print_error(f"Error checking webhook secret: {str(e)}")
        return False

def test_connect_account_creation():
    """Test 4: Connect Account Creation functionality"""
    print_test("Connect Account Creation Service")
    
    try:
        # Get or create a test user
        test_user, created = User.objects.get_or_create(
            email='<EMAIL>',
            defaults={
                'username': 'test_connect_user',
                'first_name': 'Test',
                'last_name': 'Connect',
                'is_active': True
            }
        )
        
        if created:
            print(f"   Created test user: {test_user.email}")
        else:
            print(f"   Using existing test user: {test_user.email}")
        
        # Test UserTransferService initialization
        transfer_service = UserTransferService()
        print_success("UserTransferService initialized successfully")
        
        # Check if user already has a payment profile
        existing_profile = UserPaymentProfile.objects.filter(user=test_user).first()
        if existing_profile:
            print(f"   User already has payment profile: {existing_profile.stripe_account_id}")
            return True
        
        print("   User ready for Connect account creation")
        print("   Note: Actual account creation requires manual testing via API")
        return True
        
    except Exception as e:
        print_error(f"Error testing Connect account creation: {str(e)}")
        return False

def test_webhook_endpoints():
    """Test 5: Webhook endpoint accessibility"""
    print_test("Webhook Endpoint Configuration")
    
    try:
        backend_url = getattr(settings, 'BACKEND_URL', 'http://localhost:8000')
        
        # Test standard webhook endpoint
        standard_webhook = f"{backend_url}/api/billing/webhook/"
        print(f"   Standard webhook: {standard_webhook}")
        
        # Test Connect webhook endpoint
        connect_webhook = f"{backend_url}/api/billing/webhooks/connect/"
        print(f"   Connect webhook: {connect_webhook}")
        
        print_success("Webhook endpoints configured")
        print("   Note: Endpoints need to be accessible from Stripe")
        print("   For local testing, use ngrok or similar tunneling service")
        return True
        
    except Exception as e:
        print_error(f"Error checking webhook endpoints: {str(e)}")
        return False

def test_platform_fee_configuration():
    """Test 6: Platform fee configuration"""
    print_test("Platform Fee Configuration")
    
    try:
        fee_percent = getattr(settings, 'STRIPE_APPLICATION_FEE_PERCENT', 0)
        
        if fee_percent <= 0:
            print_warning("Platform fee not configured or set to 0")
            return False
        
        print_success(f"Platform fee configured: {fee_percent}%")
        
        if fee_percent > 10:
            print_warning(f"Platform fee seems high: {fee_percent}%")
        
        return True
        
    except Exception as e:
        print_error(f"Error checking platform fee: {str(e)}")
        return False

def test_frontend_urls():
    """Test 7: Frontend URL configuration for OAuth flow"""
    print_test("Frontend URL Configuration")
    
    try:
        frontend_url = getattr(settings, 'FRONTEND_URL', '')
        backend_url = getattr(settings, 'BACKEND_URL', '')
        
        if not frontend_url:
            print_error("FRONTEND_URL not configured")
            return False
        
        if not backend_url:
            print_error("BACKEND_URL not configured")
            return False
        
        print_success("Frontend/Backend URLs configured")
        print(f"   Frontend: {frontend_url}")
        print(f"   Backend: {backend_url}")
        
        # Check OAuth redirect URLs
        refresh_url = getattr(settings, 'STRIPE_CONNECT_REFRESH_URL', '')
        return_url = getattr(settings, 'STRIPE_CONNECT_RETURN_URL', '')
        
        if refresh_url and return_url:
            print_success("OAuth redirect URLs configured")
            print(f"   Refresh URL: {refresh_url}")
            print(f"   Return URL: {return_url}")
        
        return True
        
    except Exception as e:
        print_error(f"Error checking URLs: {str(e)}")
        return False

def main():
    """Run all Stripe Connect configuration tests"""
    print_header("Stripe Connect Configuration Test Suite")
    print("Testing Ravid Healthcare Platform Stripe Connect Setup")
    
    tests = [
        ("Basic Stripe Connectivity", test_basic_stripe_connectivity),
        ("Connect Client ID", test_connect_client_id),
        ("Connect Webhook Secret", test_connect_webhook_secret),
        ("Connect Account Creation", test_connect_account_creation),
        ("Webhook Endpoints", test_webhook_endpoints),
        ("Platform Fee Configuration", test_platform_fee_configuration),
        ("Frontend URLs", test_frontend_urls),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print_error(f"{test_name} test failed with exception: {str(e)}")
    
    print_header("Test Results Summary")
    print(f"Tests Passed: {passed}/{total}")
    
    if passed == total:
        print_success("All tests passed! Stripe Connect is properly configured.")
    elif passed >= total - 1:
        print_warning("Most tests passed. Check warnings above.")
    else:
        print_error("Multiple tests failed. Review configuration.")
    
    print_header("Next Steps")
    if passed < total:
        print("1. Fix any configuration issues shown above")
        print("2. Set up Connect webhook endpoint in Stripe Dashboard")
        print("3. Test Connect account creation via API")
    else:
        print("1. Set up webhook endpoint in Stripe Dashboard")
        print("2. Test Connect account creation via API")
        print("3. Test end-to-end payment flow")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
