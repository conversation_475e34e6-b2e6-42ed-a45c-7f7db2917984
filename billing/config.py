"""
Configuration settings for the billing app
"""
from django.conf import settings
from .constants import WebhookEvents


class BillingConfig:
    """Central configuration for billing app"""
    
    STRIPE_API_VERSION = "2023-10-16"
    DEFAULT_CURRENCY = "usd"
    RATE_LIMIT_TIMEOUT = 60
    CHECKOUT_SESSION_TIMEOUT = 60  # seconds
    
    # Stripe configuration
    @classmethod
    def get_stripe_keys(cls):
        """Get Stripe API keys from Django settings"""
        return {
            'publishable_key': getattr(settings, 'STRIPE_PUBLISHABLE_KEY', ''),
            'secret_key': getattr(settings, 'STRIPE_SECRET_KEY', ''),
            'webhook_secret': getattr(settings, 'STRIPE_WEBHOOK_SECRET', ''),
            'test_webhook_secret': getattr(settings, 'STRIPE_TEST_WEBHOOK_SECRET', ''),
            'test_webhook_secret_billing': getattr(settings, 'STRIPE_TEST_WEBHOOK_SECRET_BILLING', ''),
        }
    
    @classmethod
    def get_webhook_handlers(cls):
        """Get mapping of webhook events to handler methods"""
        return {
            WebhookEvents.CHECKOUT_SESSION_COMPLETED: 'handle_checkout_completed',
            WebhookEvents.PAYMENT_INTENT_SUCCEEDED: 'handle_payment_succeeded',
            WebhookEvents.PAYMENT_INTENT_FAILED: 'handle_payment_failed',
            WebhookEvents.SUBSCRIPTION_CREATED: 'handle_subscription_created',
            WebhookEvents.SUBSCRIPTION_UPDATED: 'handle_subscription_updated',
            WebhookEvents.SUBSCRIPTION_DELETED: 'handle_subscription_deleted',
            WebhookEvents.SUBSCRIPTION_QUANTITY_UPDATED: 'handle_subscription_quantity_updated',
            WebhookEvents.INVOICE_PAYMENT_SUCCEEDED: 'handle_invoice_payment_succeeded',
            WebhookEvents.INVOICE_PAYMENT_FAILED: 'handle_invoice_payment_failed',
        }
    
    @classmethod
    def get_frontend_urls(cls):
        """Get frontend URLs for redirects"""
        frontend_url = getattr(settings, 'FRONTEND_URL', 'http://localhost:3000')
        return {
            'payment_success': f"{frontend_url}",
            'payment_cancel': f"{frontend_url}",
            'onboarding_refresh': f"{frontend_url}",
            'onboarding_complete': f"{frontend_url}",
        }
    
    @classmethod
    def is_live_mode(cls):
        """Check if Stripe is in live mode"""
        return getattr(settings, 'STRIPE_LIVE_MODE', False)
    
    @classmethod
    def get_platform_fee_defaults(cls):
        """Get default platform fee configuration"""
        return {
            'percentage': 2.9,  # 2.9%
            'fixed_amount': 30,  # 30 cents
        }
    
    @classmethod
    def get_cache_timeouts(cls):
        """Get cache timeout configurations"""
        return {
            'customer_data': 300,  # 5 minutes
            'product_prices': 600,  # 10 minutes
            'rate_limit': 60,  # 1 minute
        }
    
    @classmethod
    def get_retry_config(cls):
        """Get retry configuration for external API calls"""
        return {
            'max_attempts': 3,
            'min_wait': 4,
            'max_wait': 10,
            'multiplier': 1,
        }
    
    @classmethod
    def get_logging_config(cls):
        """Get logging configuration for billing operations"""
        return {
            'level': getattr(settings, 'BILLING_LOG_LEVEL', 'INFO'),
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        }
    
    @classmethod
    def get_webhook_tolerance(cls):
        """Get webhook signature verification tolerance"""
        return getattr(settings, 'STRIPE_WEBHOOK_TOLERANCE', 300)  # 5 minutes
    
    @classmethod
    def get_default_access_duration(cls):
        """Get default access duration for services (in days)"""
        return {
            'service': 30,  # 30 days for one-time services
            'subscription': None,  # No expiration for subscriptions
            'solution': None,  # No expiration for enterprise solutions
        }
