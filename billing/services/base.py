"""
Base service classes for billing operations
"""
from django.utils import timezone
from billing.models.product import Price, Product
from billing.models.subscription import Subscription
from django.core.exceptions import ValidationError
import stripe
import logging
from abc import ABC, abstractmethod
from django.conf import settings
from tenacity import retry, stop_after_attempt, wait_exponential

from ..config import BillingConfig
from ..exceptions import StripeAPIError, map_stripe_error

logger = logging.getLogger(__name__)


class BaseStripeService:
    """Base class for all Stripe-related services"""
    
    def __init__(self):
        self._configure_stripe()
    
    def _configure_stripe(self):
        """Configure Stripe API settings"""
        if not hasattr(settings, 'STRIPE_SECRET_KEY') or not settings.STRIPE_SECRET_KEY:
            raise StripeAPIError("Stripe secret key is not configured in Django settings")

        stripe.api_key = settings.STRIPE_SECRET_KEY
        stripe.api_version = BillingConfig.STRIPE_API_VERSION

        # Validate API key format
        if not settings.STRIPE_SECRET_KEY.startswith(('sk_test_', 'sk_live_')):
            raise StripeAPIError("Invalid Stripe secret key format")
    
    @retry(
        stop=stop_after_attempt(BillingConfig.get_retry_config()['max_attempts']),
        wait=wait_exponential(
            multiplier=BillingConfig.get_retry_config()['multiplier'],
            min=BillingConfig.get_retry_config()['min_wait'],
            max=BillingConfig.get_retry_config()['max_wait']
        )
    )
    def _make_stripe_request(self, func, *args, **kwargs):
        """Make a Stripe API request with retry logic and error handling"""
        try:
            self._log_operation(f"Making Stripe API call: {func.__name__}", {
                'function_args': str(args)[:200],  # Truncate for logging
                'function_kwargs': str(kwargs)[:200]
            })
            result = func(*args, **kwargs)
            self._log_operation(f"Stripe API call successful: {func.__name__}")
            return result
        except stripe.error.AuthenticationError as e:
            logger.error(f"Stripe authentication error in {func.__name__}: {str(e)}")
            raise StripeAPIError(f"Stripe authentication failed: {str(e)}")
        except stripe.error.InvalidRequestError as e:
            logger.error(f"Stripe invalid request error in {func.__name__}: {str(e)}")
            raise StripeAPIError(f"Invalid request to Stripe: {str(e)}")
        except stripe.error.APIConnectionError as e:
            logger.error(f"Stripe connection error in {func.__name__}: {str(e)}")
            raise StripeAPIError(f"Failed to connect to Stripe: {str(e)}")
        except stripe.error.StripeError as e:
            logger.error(f"Stripe API error in {func.__name__}: {str(e)}")
            raise map_stripe_error(e)
        except Exception as e:
            logger.error(f"Unexpected error in Stripe API call {func.__name__}: {str(e)}")
            raise StripeAPIError(f"Unexpected error: {str(e)}")
    
    def _log_operation(self, operation, details=None):
        """Log service operations for debugging"""
        logger.info(f"Billing operation: {operation}", extra=details or {})
    
    def _validate_required_params(self, params, required_fields):
        """Validate that required parameters are present"""
        missing_fields = [field for field in required_fields if not params.get(field)]
        if missing_fields:
            raise ValueError(f"Missing required parameters: {', '.join(missing_fields)}")


class PaymentProcessorInterface(ABC):
    """Interface for payment processing services"""
    
    @abstractmethod
    def create_customer(self, user):
        """Create a customer for payment processing"""
        pass
    
    @abstractmethod
    def create_payment_intent(self, amount, customer, metadata=None):
        """Create a payment intent"""
        pass
    
    @abstractmethod
    def create_checkout_session(self, customer, price, success_url, cancel_url, metadata=None):
        """Create a checkout session"""
        pass


class WebhookHandlerInterface(ABC):
    """Interface for webhook handling services"""
    
    @abstractmethod
    def handle_event(self, event):
        """Handle a webhook event"""
        pass
    
    @abstractmethod
    def verify_signature(self, payload, signature, secret):
        """Verify webhook signature"""
        pass


class SubscriptionManagerInterface(ABC):
    """Interface for subscription management services"""
    
    @abstractmethod
    def create_subscription(self, customer, price, quantity=1):
        """Create a subscription"""
        pass
    
    @abstractmethod
    def update_subscription(self, subscription_id, **kwargs):
        """Update a subscription"""
        pass
    
    @abstractmethod
    def cancel_subscription(self, subscription_id):
        """Cancel a subscription"""
        pass


# Backward compatibility class
class StripeService(BaseStripeService):
    """
    Backward compatibility class that delegates to specialized services
    This allows existing code to continue working while we migrate
    """
    
    def __init__(self):
        super().__init__()
        # Import here to avoid circular imports
        self._customer_service = None
        self._payment_service = None
        self._subscription_service = None
        self._webhook_service = None
        self._product_sync_service = None
        self._user_transfer_service = None
        self._payment_link_service = None
        self._unified_payment_service = None
    
    @property
    def customer_service(self):
        if self._customer_service is None:
            from .customer_service import CustomerService
            self._customer_service = CustomerService()
        return self._customer_service
    
    @property
    def payment_service(self):
        if self._payment_service is None:
            from .payment_service import PaymentService
            self._payment_service = PaymentService()
        return self._payment_service
    
    @property
    def subscription_service(self):
        if self._subscription_service is None:
            from .subscription_service import SubscriptionService
            self._subscription_service = SubscriptionService()
        return self._subscription_service
    
    @property
    def webhook_service(self):
        if self._webhook_service is None:
            from .webhook_service import WebhookService
            self._webhook_service = WebhookService()
        return self._webhook_service
    
    @property
    def product_sync_service(self):
        if self._product_sync_service is None:
            from .product_sync_service import ProductSyncService
            self._product_sync_service = ProductSyncService()
        return self._product_sync_service
    
    @property
    def user_transfer_service(self):
        if self._user_transfer_service is None:
            from .user_transfer_service import UserTransferService
            self._user_transfer_service = UserTransferService()
        return self._user_transfer_service
    
    @property
    def payment_link_service(self):
        if self._payment_link_service is None:
            from .payment_link_service import PaymentLinkService
            self._payment_link_service = PaymentLinkService()
        return self._payment_link_service
    
    @property
    def unified_payment_service(self):
        if self._unified_payment_service is None:
            from .unified_payment_service import UnifiedPaymentService
            self._unified_payment_service = UnifiedPaymentService()
        return self._unified_payment_service
    
    # Delegate methods to appropriate services for backward compatibility
    @classmethod
    def create_or_update_customer(cls, user):
        """Delegate to CustomerService"""
        return cls().customer_service.create_or_update_customer(user)
    
    @classmethod
    def create_checkout_session(cls, customer, price, success_url, cancel_url, metadata=None, mode='subscription'):
        """Delegate to PaymentService"""
        return cls().payment_service.create_checkout_session(customer, price, success_url, cancel_url, metadata, mode)
    
    @classmethod
    def handle_webhook_event(cls, event):
        """Delegate to WebhookService"""
        return cls().webhook_service.handle_event(event)
    
    @classmethod
    def sync_products(cls):
        """Delegate to ProductSyncService"""
        return cls().product_sync_service.sync_products()

    @classmethod
    def get_service_stripe_info(cls, service):
        """Get Stripe product and price information for a service"""
        return cls._get_content_stripe_info(service, 'service')

    @classmethod
    def get_subscription_plan_stripe_info(cls, plan):
        """Get Stripe product and price information for a subscription plan"""
        return cls._get_content_stripe_info(plan, 'subscription_plan')

    @classmethod
    def get_solution_stripe_info(cls, solution):
        """Get Stripe product and price information for a solution"""
        return cls._get_content_stripe_info(solution, 'solution')

    @classmethod
    def _get_content_stripe_info(cls, content_obj, content_type):
        """Generic method to get Stripe product and price information"""
        try:
            from ..models import Product, Price

            # Build filter based on content type
            filter_kwargs = {'active': True}
            if content_type == 'service':
                filter_kwargs['service'] = content_obj
            elif content_type == 'subscription_plan':
                filter_kwargs['subscription_plan'] = content_obj
            elif content_type == 'solution':
                filter_kwargs['solution'] = content_obj

            product = Product.objects.filter(**filter_kwargs).first()

            if not product:
                return None

            # Get active prices from our database
            db_prices = Price.objects.filter(
                product=product,
                active=True
            ).values('stripe_price_id', 'unit_amount', 'currency', 'recurring')

            # Get active prices from Stripe
            stripe_prices = stripe.Price.list(
                product=product.stripe_product_id,
                active=True
            ).data

            # Create a set of active Stripe price IDs
            active_stripe_price_ids = {price.id for price in stripe_prices}

            # Filter prices to only include those that are active in both our DB and Stripe
            active_prices = [
                price for price in db_prices
                if price['stripe_price_id'] in active_stripe_price_ids
            ]

            return {
                'stripe_product_id': product.stripe_product_id,
                'name': product.name,
                'description': product.description,
                'prices': active_prices
            }

        except Exception as e:
            logger.error(f"Error getting Stripe info for {content_type} {content_obj.id}: {str(e)}")
            return None
    @staticmethod
    def _sync_service(service):
        """Sync a service with Stripe"""
        try:
            # Create or update Stripe product
            product_data = {
                'name': service.name,
                'description': service.description,
                'metadata': {
                    'type': 'service',
                    'content_id': str(service.id)
                }
            }
            
            # Try to get existing product
            try:
                product = Product.objects.get(content_id=service.id)
                stripe_product = stripe.Product.modify(
                    product.stripe_product_id,
                    **product_data
                )
            except Product.DoesNotExist:
                stripe_product = stripe.Product.create(**product_data)
                product = Product.objects.create(
                    stripe_product_id=stripe_product.id,
                    content_id=service.id,
                    name=service.name,
                    description=service.description,
                    product_type='service',
                    service=service
                )
            
            # Sync prices with original price (no promo applied)
            StripeService._sync_product_prices(product, service, apply_promo=False)
            
            # Sync promotions
            StripeService._sync_service_promotions(service)
                
        except Exception as e:
            logger.error(f"Error syncing service {service.id} with Stripe: {str(e)}")
            raise

    
    @staticmethod
    def _sync_product_prices(product, content_model, apply_promo=False):
        """
        Sync product prices with Stripe
        """
        try:
            # Get original price amount
            price_amount = content_model.price
            has_discount = False
            discount_percentage = None
            
            # Only apply promo if requested
            if apply_promo and hasattr(content_model, 'active_promotions'):
                active_promos = content_model.active_promotions.filter(
                    is_active=True,
                    start_date__lte=timezone.now(),
                    end_date__gte=timezone.now()
                )
                if active_promos.exists():
                    promo = active_promos.order_by('-discount_percentage').first()
                    price_amount = content_model.get_discounted_price()
                    has_discount = True
                    discount_percentage = promo.discount_percentage
            
            # Convert price_amount to cents for Stripe
            unit_amount = int(price_amount * 100)
            
            # Check if a price with the same amount already exists and is active
            existing_prices = stripe.Price.list(
                product=product.stripe_product_id,
                active=True
            ).data
            
            # Determine if this is a subscription price
            is_subscription = product.product_type in ['subscription', 'solution']
            recurring_config = {
                'interval': 'month',  # Default to monthly for solutions
                'interval_count': 1
            } if is_subscription else None
            
            matching_price = next(
                (p for p in existing_prices 
                if p.unit_amount == unit_amount 
                and p.currency.lower() == 'usd'
                and p.metadata.get('has_discount') == str(has_discount)
                and p.metadata.get('discount_percentage') == str(discount_percentage)),
                None
            )
            
            if matching_price:
                # If a matching price exists, use it
                stripe_price = matching_price
                logger.info(f"Using existing price {stripe_price.id} for product {product.stripe_product_id}")
            else:
                # Archive all other active prices
                for price in existing_prices:
                    stripe.Price.modify(price.id, active=False)
                    logger.info(f"Archived price {price.id} for product {product.stripe_product_id}")
                
                # Create new price in Stripe
                price_params = {
                    'product': product.stripe_product_id,
                    'unit_amount': unit_amount,
                    'currency': 'usd',
                    'metadata': {
                        'content_id': str(content_model.id),
                        'has_discount': str(has_discount),
                        'discount_percentage': str(discount_percentage)
                    }
                }
                
                # Add recurring configuration for subscription prices
                if is_subscription:
                    price_params['recurring'] = recurring_config
                
                stripe_price = stripe.Price.create(**price_params)
                logger.info(f"Created new price {stripe_price.id} for product {product.stripe_product_id}")
            
            # Update or create price in database
            Price.objects.update_or_create(
                stripe_price_id=stripe_price.id,
                defaults={
                    'product': product,
                    'active': True,
                    'unit_amount': unit_amount,
                    'currency': 'usd',
                    'recurring': recurring_config
                }
            )
            
        except Exception as e:
            logger.error(f"Error syncing product prices: {str(e)}")
            raise

    @staticmethod
    def _sync_subscription_plan(plan):
        """
        Sync a SubscriptionPlan model to Stripe Product
        """
        try:
            product = Product.objects.get(
                product_type='subscription',
                subscription_plan=plan
            )
            stripe_product = stripe.Product.modify(
                product.stripe_product_id,
                name=plan.name,
                description=plan.description,
                active=plan.is_active,
                metadata={
                    'type': 'subscription',
                    'content_id': str(plan.id)
                }
            )
        except Product.DoesNotExist:
            stripe_product = stripe.Product.create(
                name=plan.name,
                description=plan.description,
                metadata={
                    'type': 'subscription',
                    'content_id': str(plan.id)
                }
            )
            product = Product.objects.create(
                stripe_product_id=stripe_product.id,
                name=plan.name,
                description=plan.description,
                active=plan.is_active,
                product_type='subscription',
                subscription_plan=plan,
                content_id=plan.id
            )
        
        # Sync prices with original price (no promo applied)
        StripeService._sync_product_prices(product, plan, apply_promo=False)

    @staticmethod
    def _sync_solution(solution):
        """
        Sync a Solution model to Stripe Product
        """
        try:
            product = Product.objects.get(
                product_type='solution',
                solution=solution
            )
            stripe_product = stripe.Product.modify(
                product.stripe_product_id,
                name=solution.name,
                description=solution.description,
                active=solution.is_active,
                metadata={
                    'type': 'solution',
                    'solution_type': solution.solution_type,
                    'content_id': str(solution.id)
                }
            )
        except Product.DoesNotExist:
            stripe_product = stripe.Product.create(
                name=solution.name,
                description=solution.description,
                metadata={
                    'type': 'solution',
                    'solution_type': solution.solution_type,
                    'content_id': str(solution.id)
                }
            )
            product = Product.objects.create(
                stripe_product_id=stripe_product.id,
                name=solution.name,
                description=solution.description,
                active=solution.is_active,
                product_type='solution',
                solution=solution,
                content_id=solution.id
            )
        
        # Sync prices with original price (no promo applied)
        StripeService._sync_product_prices(product, solution, apply_promo=False)

    @staticmethod
    def create_subscription(customer, price, quantity=1):
        """
        Create a new subscription
        """
        try:
            # Create subscription in Stripe
            subscription = stripe.Subscription.create(
                customer=customer.stripe_customer_id,
                items=[{
                    'price': price.stripe_price_id,
                    'quantity': quantity
                }],
                payment_behavior='default_incomplete',
                expand=['latest_invoice.payment_intent']
            )
            
            # Create subscription record
            subscription_record = Subscription.objects.create(
                customer=customer,
                stripe_subscription_id=subscription.id,
                stripe_subscription_item_id=subscription.items.data[0].id,
                status=subscription.status,
                current_period_start=timezone.datetime.fromtimestamp(subscription.current_period_start),
                current_period_end=timezone.datetime.fromtimestamp(subscription.current_period_end)
            )
            
            return subscription_record
            
        except Exception as e:
            logger.error(f"Error creating subscription: {str(e)}")
            raise ValidationError(f"Error creating subscription: {str(e)}")

    @staticmethod
    def update_subscription_quantity(subscription_id, quantity):
        """
        Update subscription quantity (number of seats)
        """
        try:
            subscription = Subscription.objects.get(id=subscription_id)
            
            # Update subscription in Stripe
            stripe_subscription = stripe.Subscription.modify(
                subscription.stripe_subscription_id,
                items=[{
                    'id': subscription.stripe_subscription_item_id,
                    'quantity': quantity
                }]
            )
            
            # Update subscription record
            subscription.status = stripe_subscription.status
            subscription.current_period_start = timezone.datetime.fromtimestamp(stripe_subscription.current_period_start)
            subscription.current_period_end = timezone.datetime.fromtimestamp(stripe_subscription.current_period_end)
            subscription.save()
            
            return subscription
            
        except Subscription.DoesNotExist:
            raise ValidationError("Subscription not found")
        except Exception as e:
            logger.error(f"Error updating subscription: {str(e)}")
            raise ValidationError(f"Error updating subscription: {str(e)}")

    @classmethod
    def get_service_stripe_info(cls, service):
        """Get Stripe product and price information for a service"""
        try:
            product = Product.objects.filter(
                service=service,
                active=True
            ).first()
            
            if not product:
                return None
                
            # Get active prices from our database
            db_prices = Price.objects.filter(
                product=product,
                active=True
            ).values('stripe_price_id', 'unit_amount', 'currency', 'recurring')
            
            # Get active prices from Stripe
            stripe_prices = stripe.Price.list(
                product=product.stripe_product_id,
                active=True
            ).data
            
            # Create a set of active Stripe price IDs
            active_stripe_price_ids = {price.id for price in stripe_prices}
            
            # Filter prices to only include those that are active in both our DB and Stripe
            active_prices = [
                price for price in db_prices 
                if price['stripe_price_id'] in active_stripe_price_ids
            ]
            
            return {
                'stripe_product_id': product.stripe_product_id,
                'name': product.name,
                'description': product.description,
                'prices': active_prices
            }
            
        except Exception as e:
            logger.error(f"Error getting Stripe info for service {service.id}: {str(e)}")
            return None

    @classmethod
    def get_subscription_plan_stripe_info(cls, plan):
        """Get Stripe product and price information for a subscription plan"""
        try:
            product = Product.objects.filter(
                subscription_plan=plan,
                active=True
            ).first()
            
            if not product:
                return None
                
            # Get active prices from our database
            db_prices = Price.objects.filter(
                product=product,
                active=True
            ).values('stripe_price_id', 'unit_amount', 'currency', 'recurring')
            
            # Get active prices from Stripe
            stripe_prices = stripe.Price.list(
                product=product.stripe_product_id,
                active=True
            ).data
            
            # Create a set of active Stripe price IDs
            active_stripe_price_ids = {price.id for price in stripe_prices}
            
            # Filter prices to only include those that are active in both our DB and Stripe
            active_prices = [
                price for price in db_prices 
                if price['stripe_price_id'] in active_stripe_price_ids
            ]
            
            return {
                'stripe_product_id': product.stripe_product_id,
                'name': product.name,
                'description': product.description,
                'prices': active_prices
            }
            
        except Exception as e:
            logger.error(f"Error getting Stripe info for subscription plan {plan.id}: {str(e)}")
            return None

    @classmethod
    def get_solution_stripe_info(cls, solution):
        """Get Stripe product and price information for a solution"""
        try:
            product = Product.objects.filter(
                solution=solution,
                active=True
            ).first()
            
            if not product:
                return None
                
            # Get active prices from our database
            db_prices = Price.objects.filter(
                product=product,
                active=True
            ).values('stripe_price_id', 'unit_amount', 'currency', 'recurring')
            
            # Get active prices from Stripe
            stripe_prices = stripe.Price.list(
                product=product.stripe_product_id,
                active=True
            ).data
            
            # Create a set of active Stripe price IDs
            active_stripe_price_ids = {price.id for price in stripe_prices}
            
            # Filter prices to only include those that are active in both our DB and Stripe
            active_prices = [
                price for price in db_prices 
                if price['stripe_price_id'] in active_stripe_price_ids
            ]
            
            return {
                'stripe_product_id': product.stripe_product_id,
                'name': product.name,
                'description': product.description,
                'prices': active_prices
            }
            
        except Exception as e:
            logger.error(f"Error getting Stripe info for solution {solution.id}: {str(e)}")
            return None

    @staticmethod
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    def _delete_stripe_product(product):
        """
        Archive a Stripe product and its associated prices
        """
        try:
            logger.info(f"Starting archiving of Stripe product {product.stripe_product_id}")
            
            # Archive all prices associated with the product
            existing_prices = stripe.Price.list(product=product.stripe_product_id)
            logger.info(f"Found {len(existing_prices.data)} prices for product {product.stripe_product_id}")
            
            for price in existing_prices.data:
                try:
                    # Archive the price instead of deleting it
                    stripe.Price.modify(price.id, active=False)
                    logger.info(f"Archived Stripe price {price.id}")
                except stripe.error.StripeError as e:
                    logger.error(f"Error archiving Stripe price {price.id}: {str(e)}")
                    raise
            
            # Archive the product from Stripe first
            try:
                # First verify the product exists in Stripe
                stripe_product = stripe.Product.retrieve(product.stripe_product_id)
                if stripe_product:
                    # Then archive it
                    stripe.Product.modify(product.stripe_product_id, active=False)
                    logger.info(f"Archived Stripe product {product.stripe_product_id}")
                else:
                    logger.warning(f"Product {product.stripe_product_id} not found in Stripe")
            except stripe.error.InvalidRequestError as e:
                if "No such product" in str(e):
                    logger.warning(f"Product {product.stripe_product_id} not found in Stripe")
                else:
                    raise
            except stripe.error.StripeError as e:
                logger.error(f"Stripe error archiving product {product.stripe_product_id}: {str(e)}")
                raise
            
            # Only delete from database after successful Stripe archiving
            product.delete()
            logger.info(f"Deleted product {product.id} from database")
            
        except stripe.error.StripeError as e:
            logger.error(f"Error archiving Stripe product {product.stripe_product_id}: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error archiving Stripe product: {str(e)}")
            raise

    @staticmethod
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    def delete_service_stripe_product(service):
        """
        Archive Stripe product and prices for a service
        """
        try:
            logger.info(f"Starting archiving of Stripe product for service {service.id}")
            
            # Find product by service or content_id
            product = Product.objects.filter(
                product_type='service',
                service=service
            ).first()
            
            # If not found by service, try content_id
            if not product:
                product = Product.objects.filter(
                    product_type='service',
                    content_id=service.id
                ).first()
                if product:
                    logger.info(f"Found product {product.id} with Stripe ID {product.stripe_product_id} for service {service.id} using content_id")
            
            # If still not found, try to find by metadata
            if not product:
                try:
                    # Try to find the product directly in Stripe by metadata
                    stripe_products = stripe.Product.list(
                        active=True,
                        limit=100  # Increase limit to ensure we find the product
                    )
                    
                    # Filter products by metadata after retrieving them
                    matching_products = [
                        p for p in stripe_products.data 
                        if p.metadata.get('type') == 'service' and p.metadata.get('content_id') == str(service.id)
                    ]
                    
                    if matching_products:
                        stripe_product = matching_products[0]
                        logger.info(f"Found Stripe product {stripe_product.id} for service {service.id} using Stripe API")
                        
                        # Create a temporary product object to delete
                        product = Product(
                            stripe_product_id=stripe_product.id,
                            name=stripe_product.name,
                            product_type='service',
                            content_id=service.id
                        )
                    else:
                        logger.warning(f"No matching Stripe product found for service {service.id}")
                        return False
                except stripe.error.StripeError as e:
                    logger.error(f"Error finding Stripe product for service {service.id}: {str(e)}")
                    raise
            
            if product and product.stripe_product_id:
                logger.info(f"Found product {product.id} with Stripe ID {product.stripe_product_id} for service {service.id}")
                StripeService._delete_stripe_product(product)
                logger.info(f"Successfully archived Stripe product for service {service.id}")
                return True
            else:
                logger.warning(f"No valid Stripe product found for service {service.id}")
                return False
            
        except stripe.error.StripeError as e:
            logger.error(f"Stripe error archiving product for service {service.id}: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error archiving Stripe product for service {service.id}: {str(e)}")
            raise

    @staticmethod
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    def delete_subscription_plan_stripe_product(plan):
        """
        Archive Stripe product and prices for a subscription plan
        """
        try:
            logger.info(f"Starting archiving of Stripe product for subscription plan {plan.id}")
            
            # Find product by subscription_plan or content_id
            product = Product.objects.filter(
                product_type='subscription',
                subscription_plan=plan
            ).first()
            
            # If not found by subscription_plan, try content_id
            if not product:
                product = Product.objects.filter(
                    product_type='subscription',
                    content_id=plan.id
                ).first()
                if product:
                    logger.info(f"Found product {product.id} with Stripe ID {product.stripe_product_id} for subscription plan {plan.id} using content_id")
            
            # If still not found, try to find by metadata
            if not product:
                try:
                    # Try to find the product directly in Stripe by metadata
                    stripe_products = stripe.Product.list(
                        active=True,
                        limit=100  # Increase limit to ensure we find the product
                    )
                    
                    # Filter products by metadata after retrieving them
                    matching_products = [
                        p for p in stripe_products.data 
                        if p.metadata.get('type') == 'subscription' and p.metadata.get('content_id') == str(plan.id)
                    ]
                    
                    if matching_products:
                        stripe_product = matching_products[0]
                        logger.info(f"Found Stripe product {stripe_product.id} for subscription plan {plan.id} using Stripe API")
                        
                        # Create a temporary product object to delete
                        product = Product(
                            stripe_product_id=stripe_product.id,
                            name=stripe_product.name,
                            product_type='subscription',
                            content_id=plan.id
                        )
                    else:
                        logger.warning(f"No matching Stripe product found for subscription plan {plan.id}")
                        return False
                except stripe.error.StripeError as e:
                    logger.error(f"Error finding Stripe product for subscription plan {plan.id}: {str(e)}")
                    raise
            
            if product and product.stripe_product_id:
                logger.info(f"Found product {product.id} with Stripe ID {product.stripe_product_id} for subscription plan {plan.id}")
                StripeService._delete_stripe_product(product)
                logger.info(f"Successfully archived Stripe product for subscription plan {plan.id}")
                return True
            else:
                logger.warning(f"No valid Stripe product found for subscription plan {plan.id}")
                return False
            
        except stripe.error.StripeError as e:
            logger.error(f"Stripe error archiving product for subscription plan {plan.id}: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error archiving Stripe product for subscription plan {plan.id}: {str(e)}")
            raise

    @staticmethod
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    def delete_solution_stripe_product(solution):
        """
        Archive Stripe product and prices for a solution
        """
        try:
            logger.info(f"Starting archiving of Stripe product for solution {solution.id}")
            
            # Find product by solution or content_id
            product = Product.objects.filter(
                product_type='solution',
                solution=solution
            ).first()
            
            # If not found by solution, try content_id
            if not product:
                product = Product.objects.filter(
                    product_type='solution',
                    content_id=solution.id
                ).first()
                if product:
                    logger.info(f"Found product {product.id} with Stripe ID {product.stripe_product_id} for solution {solution.id} using content_id")
            
            if product and product.stripe_product_id:
                logger.info(f"Found product {product.id} with Stripe ID {product.stripe_product_id} for solution {solution.id}")
                try:
                    # Try to retrieve the product directly from Stripe using stripe_product_id
                    stripe_product = stripe.Product.retrieve(product.stripe_product_id)
                    if stripe_product:
                        logger.info(f"Found Stripe product {stripe_product.id} for solution {solution.id}")
                        StripeService._delete_stripe_product(product)
                        logger.info(f"Successfully archived Stripe product for solution {solution.id}")
                        return True
                    else:
                        logger.warning(f"Stripe product {product.stripe_product_id} not found in Stripe")
                        return False
                except stripe.error.InvalidRequestError as e:
                    if "No such product" in str(e):
                        logger.warning(f"Stripe product {product.stripe_product_id} not found in Stripe")
                        return False
                    else:
                        raise
                except stripe.error.StripeError as e:
                    logger.error(f"Stripe error retrieving product {product.stripe_product_id}: {str(e)}")
                    raise
            else:
                logger.warning(f"No valid Stripe product found for solution {solution.id}")
                return False
            
        except stripe.error.StripeError as e:
            logger.error(f"Stripe error archiving product for solution {solution.id}: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error archiving Stripe product for solution {solution.id}: {str(e)}")
            raise

    @staticmethod
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    def delete_stripe_product_by_id(stripe_product_id):
        """
        Archive a Stripe product directly by its ID
        This can be used as a fallback if the database record is not found
        """
        try:
            logger.info(f"Starting direct archiving of Stripe product {stripe_product_id}")
            
            # Archive all prices associated with the product
            existing_prices = stripe.Price.list(product=stripe_product_id)
            logger.info(f"Found {len(existing_prices.data)} prices for product {stripe_product_id}")
            
            for price in existing_prices.data:
                try:
                    # Archive the price instead of deleting it
                    stripe.Price.modify(price.id, active=False)
                    logger.info(f"Archived Stripe price {price.id}")
                except stripe.error.StripeError as e:
                    logger.error(f"Error archiving Stripe price {price.id}: {str(e)}")
            
            # Archive the product
            try:
                # First verify the product exists in Stripe
                stripe_product = stripe.Product.retrieve(stripe_product_id)
                if stripe_product:
                    # Then archive it
                    stripe.Product.modify(stripe_product_id, active=False)
                    logger.info(f"Archived Stripe product {stripe_product_id}")
                else:
                    logger.warning(f"Product {stripe_product_id} not found in Stripe")
            except stripe.error.InvalidRequestError as e:
                if "No such product" in str(e):
                    logger.warning(f"Product {stripe_product_id} not found in Stripe")
                else:
                    raise
            except stripe.error.StripeError as e:
                logger.error(f"Stripe error archiving product {stripe_product_id}: {str(e)}")
                raise
            
            return True
            
        except stripe.error.StripeError as e:
            logger.error(f"Error archiving Stripe product {stripe_product_id}: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error archiving Stripe product: {str(e)}")
            raise

    @staticmethod
    def revoke_service_access(service_access):
        """
        Revoke access to a service
        """
        try:
            # Update service access status
            service_access.status = 'revoked'
            service_access.save()

            # If there's a payment, update its status
            if service_access.payment:
                service_access.payment.status = 'refunded'
                service_access.payment.save()

            return True
        except Exception as e:
            logger.error(f"Error revoking service access {service_access.id}: {str(e)}")
            raise

    @staticmethod
    def revoke_subscription_access(subscription_access):
        """
        Revoke access to a subscription
        """
        try:
            # Cancel subscription in Stripe
            subscription = subscription_access.subscription
            stripe_subscription = stripe.Subscription.delete(
                subscription.stripe_subscription_id
            )

            # Update subscription status
            subscription.status = 'canceled'
            subscription.canceled_at = timezone.now()
            subscription.save()

            # Update subscription access status
            subscription_access.status = 'canceled'
            subscription_access.save()

            return True
        except stripe.error.StripeError as e:
            logger.error(f"Stripe error revoking subscription access {subscription_access.id}: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Error revoking subscription access {subscription_access.id}: {str(e)}")
            raise

    @staticmethod
    def revoke_solution_access(solution_access):
        """
        Revoke access to a solution
        """
        try:
            # Cancel subscription in Stripe
            subscription = solution_access.subscription
            stripe_subscription = stripe.Subscription.delete(
                subscription.stripe_subscription_id
            )

            # Update subscription status
            subscription.status = 'canceled'
            subscription.canceled_at = timezone.now()
            subscription.save()

            # Update solution access status
            solution_access.status = 'canceled'
            solution_access.save()

            return True
        except stripe.error.StripeError as e:
            logger.error(f"Stripe error revoking solution access {solution_access.id}: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Error revoking solution access {solution_access.id}: {str(e)}")
            raise

    @staticmethod
    def _create_stripe_promo_code(promo):
        """Create a promo code in Stripe for a ServicePromotion"""
        try:
            # Get the first service that has this promotion
            service = promo.services.first()
            if not service:
                raise ValueError(f"No service found for promotion {promo.id}")
            
            # Create coupon
            stripe_coupon = stripe.Coupon.create(
                percent_off=float(promo.discount_percentage),
                duration='once',
                metadata={
                    'promo_id': str(promo.id),
                    'service_id': str(service.id)
                }
            )
            
            # Create promotion code
            stripe_promo = stripe.PromotionCode.create(
                coupon=stripe_coupon.id,
                code=promo.code,
                restrictions={
                    'minimum_amount': int(service.price * 100),  # Convert to cents
                    'minimum_amount_currency': 'usd'
                }
            )
            
            return stripe_promo.id
            
        except Exception as e:
            logger.error(f"Error creating Stripe promo code for promotion {promo.id}: {str(e)}")
            raise

    @staticmethod
    def _sync_service_promotions(service):
        """Sync all active promotions for a service to Stripe"""
        try:
            active_promos = service.active_promotions.filter(
                is_active=True,
                start_date__lte=timezone.now(),
                end_date__gte=timezone.now()
            )
            
            for promo in active_promos:
                if not promo.stripe_promo_id:
                    # Create new promo code in Stripe
                    stripe_promo_id = StripeService._create_stripe_promo_code(promo)
                    promo.stripe_promo_id = stripe_promo_id
                    promo.save()
                    
        except Exception as e:
            logger.error(f"Error syncing promotions for service {service.id}: {str(e)}")
            raise

    @staticmethod
    def get_or_create_discounted_price(service, discounted_price, promotion=None):
        """
        Lấy hoặc tạo Price Stripe cho service với giá đã giảm (theo promotion).
        """

        # Lấy product Stripe
        product = Product.objects.get(service=service, active=True)
        unit_amount = int(discounted_price * 100)
        currency = 'usd'
        promo_id = str(promotion.id) if promotion else ''

        # Tìm price đã tồn tại
        existing_prices = stripe.Price.list(
            product=product.stripe_product_id,
            active=True
        ).data

        for price in existing_prices:
            if (
                price.unit_amount == unit_amount
                and price.currency.lower() == currency
                and price.metadata.get('promotion_id', '') == promo_id
            ):
                # Đã có price đúng
                Price.objects.update_or_create(
                    stripe_price_id=price.id,
                    defaults={
                        'product': product,
                        'active': True,
                        'unit_amount': unit_amount,
                        'currency': currency,
                    }
                )
                return price.id

        # Nếu chưa có, tạo mới
        price_params = {
            'product': product.stripe_product_id,
            'unit_amount': unit_amount,
            'currency': currency,
            'metadata': {
                'content_id': str(service.id),
                'promotion_id': promo_id,
            }
        }
        stripe_price = stripe.Price.create(**price_params)
        Price.objects.update_or_create(
            stripe_price_id=stripe_price.id,
            defaults={
                'product': product,
                'active': True,
                'unit_amount': unit_amount,
                'currency': currency,
            }
        )
        return stripe_price.id

    # Add more delegation methods as needed during migration...
