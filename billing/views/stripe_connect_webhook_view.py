"""
Stripe Connect Webhook View for handling Connect-specific events
"""

import logging
import stripe
from django.conf import settings
from django.http import HttpResponse
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from django.views import View
from rest_framework import status

from ..services.webhook_service import WebhookService
from ..exceptions import WebhookError

logger = logging.getLogger(__name__)


@method_decorator(csrf_exempt, name='dispatch')
class StripeConnectWebhookView(View):
    """
    Webhook endpoint for Stripe Connect events
    
    Handles Connect-specific events like:
    - account.updated
    - account.application.deauthorized
    - transfer.created
    - transfer.updated
    """
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.webhook_service = WebhookService()
        stripe.api_key = settings.STRIPE_SECRET_KEY
    
    def post(self, request):
        """Handle incoming Stripe Connect webhook"""
        payload = request.body
        sig_header = request.META.get('HTTP_STRIPE_SIGNATURE', '')
        
        try:
            # Verify webhook signature
            secret = settings.STRIPE_CONNECT_WEBHOOK_SECRET
            if not secret:
                logger.error("STRIPE_CONNECT_WEBHOOK_SECRET not configured")
                return HttpResponse("Webhook secret not configured", status=500)
            
            if not self.webhook_service.verify_signature(payload, sig_header, secret):
                logger.error("Invalid signature for Connect webhook")
                return HttpResponse("Invalid signature", status=400)
            
            # Parse event
            try:
                event = stripe.Event.construct_from(
                    stripe.util.json.loads(payload), stripe.api_key
                )
            except ValueError as e:
                logger.error(f"Invalid payload for Connect webhook: {e}")
                return HttpResponse("Invalid payload", status=400)
            
            # Log the event
            logger.info(f"Received Connect webhook event: {event.type} - {event.id}")
            
            # Route to appropriate handler
            success = self._handle_event(event)
            
            if success:
                logger.info(f"Successfully processed Connect webhook: {event.type}")
                return HttpResponse("OK", status=200)
            else:
                logger.error(f"Failed to process Connect webhook: {event.type}")
                return HttpResponse("Processing failed", status=500)
                
        except WebhookError as e:
            logger.error(f"Webhook error: {str(e)}")
            return HttpResponse(f"Webhook error: {str(e)}", status=400)
        except Exception as e:
            logger.error(f"Unexpected error processing Connect webhook: {str(e)}")
            return HttpResponse("Internal server error", status=500)
    
    def _handle_event(self, event):
        """Route event to appropriate handler"""
        event_handlers = {
            'account.updated': self.webhook_service.handle_account_updated,
            'account.application.deauthorized': self.webhook_service.handle_account_deauthorized,
            'payment_intent.succeeded': self.webhook_service.handle_payment_succeeded,
            'payment_intent.payment_failed': self.webhook_service.handle_payment_failed,
            'transfer.created': self._handle_transfer_created,
            'transfer.updated': self._handle_transfer_updated,
        }
        
        handler = event_handlers.get(event.type)
        if handler:
            try:
                return handler(event.data.object)
            except Exception as e:
                logger.error(f"Error in handler for {event.type}: {str(e)}")
                return False
        else:
            logger.warning(f"No handler for Connect event type: {event.type}")
            return True  # Return True for unhandled events to avoid retries
    
    def _handle_transfer_created(self, transfer):
        """Handle transfer.created event"""
        try:
            logger.info(f"Processing transfer created: {transfer.id}")
            
            # Update any local records if needed
            # This is mainly for logging and tracking purposes
            metadata = getattr(transfer, 'metadata', {})
            
            logger.info(
                f"Transfer created: {transfer.id} "
                f"Amount: {transfer.amount} "
                f"Destination: {transfer.destination} "
                f"Metadata: {metadata}"
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Error handling transfer created: {str(e)}")
            return False
    
    def _handle_transfer_updated(self, transfer):
        """Handle transfer.updated event"""
        try:
            logger.info(f"Processing transfer updated: {transfer.id}")
            
            # Log transfer status changes
            logger.info(
                f"Transfer updated: {transfer.id} "
                f"Status: {getattr(transfer, 'status', 'unknown')} "
                f"Amount: {transfer.amount}"
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Error handling transfer updated: {str(e)}")
            return False 