from django.db import models
from django.conf import settings
from django.core.exceptions import ValidationError
from django.core.validators import RegexValidator
import base64
from django.core.files.base import ContentFile
import uuid
import re
import random
import string

def generate_username_from_name(first_name, last_name):
    """Generate a username from first and last name"""
    if not first_name and not last_name:
        return None

    # Create base username from name
    base = f"{first_name or ''}-{last_name or ''}".lower()
    base = re.sub(r'[^a-z0-9-]', '', base)  # Remove special chars
    base = re.sub(r'-+', '-', base)  # Replace multiple dashes with single
    base = base.strip('-')  # Remove leading/trailing dashes

    if not base:
        return None

    # Ensure it's not too long
    if len(base) > 30:
        base = base[:30].rstrip('-')

    return base


def generate_unique_username(user_instance, base_username=None):
    """Generate a unique username for a user"""
    if not base_username:
        # Try to generate from user's name
        base_username = generate_username_from_name(
            user_instance.user.first_name,
            user_instance.user.last_name
        )

    if not base_username:
        # Fallback to random username
        base_username = f"user-{''.join(random.choices(string.ascii_lowercase + string.digits, k=8))}"

    # Check if base username is available
    username = base_username
    counter = 1

    while Profile.objects.filter(username=username).exclude(pk=user_instance.pk).exists():
        username = f"{base_username}-{counter}"
        counter += 1

        # Prevent infinite loop
        if counter > 1000:
            username = f"{base_username}-{''.join(random.choices(string.ascii_lowercase + string.digits, k=4))}"
            break

    return username


class Profile(models.Model):
    user = models.OneToOneField(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    username = models.CharField(
        max_length=50,
        unique=True,
        blank=True,
        null=True,
        validators=[
            RegexValidator(
                regex=r'^[a-z0-9-]+$',
                message='Username can only contain lowercase letters, numbers, and hyphens.',
                code='invalid_username'
            ),
            RegexValidator(
                regex=r'^[a-z0-9].*[a-z0-9]$|^[a-z0-9]$',
                message='Username must start and end with a letter or number.',
                code='invalid_username_format'
            )
        ],
        help_text='Unique username for public profile URLs. Only lowercase letters, numbers, and hyphens allowed.'
    )
    clinic = models.ForeignKey('clinic.Clinic', on_delete=models.SET_NULL, null=True, blank=True)
    bio = models.TextField(blank=True, null=True)   
    profile_picture = models.CharField(max_length=255, blank=True, null=True) 
    private_profile_picture = models.CharField(max_length=255, blank=True, null=True)
    credentials = models.TextField(blank=True, null=True)
    dob = models.CharField(max_length=10, blank=True, null=True)
    genome_tested = models.BooleanField(default=False, blank=True, null=True)
    gender = models.CharField(max_length=10, blank=True, null=True)
    language = models.CharField(max_length=10, blank=True, null=True)
    blood_group = models.CharField(max_length=10, blank=True, null=True)
    digital_blood = models.CharField(max_length=10, blank=True, null=True)
    address = models.CharField(max_length=255, blank=True, null=True)
    city = models.CharField(max_length=255, blank=True, null=True)
    state = models.CharField(max_length=255, blank=True, null=True)
    zipcode = models.CharField(max_length=20, blank=True, null=True)
    country = models.CharField(max_length=100, blank=True, null=True)
    mobile = models.CharField(max_length=20, blank=True, null=True)
    homephone = models.CharField(max_length=20, blank=True, null=True)
    # New fields for doctor profile
    title = models.CharField(max_length=100, blank=True, null=True)
    education = models.TextField(blank=True, null=True)
    about_me = models.TextField(blank=True, null=True)
    practices = models.TextField(blank=True, null=True)
    affiliations = models.TextField(blank=True, null=True)
    research_papers = models.TextField(blank=True, null=True)
    awards = models.TextField(blank=True, null=True)
    youtube_videos = models.TextField(blank=True, null=True)
    locations = models.TextField(blank=True, null=True)
    speciality = models.CharField(max_length=255, blank=True, null=True)
    is_credentials_verified = models.BooleanField(default=False)
    credential_verification_notes = models.TextField(blank=True, null=True)
    credential_submitted_at = models.DateTimeField(null=True, blank=True)
    preferred_language = models.CharField(max_length=10, choices=settings.LANGUAGES, default=settings.LANGUAGE_CODE) 
    
    show_education = models.BooleanField(default=True)
    show_research_papers = models.BooleanField(default=True)
    show_awards = models.BooleanField(default=True)
    show_youtube_videos = models.BooleanField(default=True)
    show_practice_locations = models.BooleanField(default=True)
    show_credential_documents = models.BooleanField(default=True)
    show_custom_information = models.BooleanField(default=True)
    
    is_public_profile = models.BooleanField(default=False)

    def clean(self):
        """Validate username"""
        if self.username:
            # Additional validation
            if len(self.username) < 3:
                raise ValidationError({'username': 'Username must be at least 3 characters long.'})

            if '--' in self.username:
                raise ValidationError({'username': 'Username cannot contain consecutive hyphens.'})

            # Check for reserved usernames
            reserved_usernames = [
                'admin', 'api', 'www', 'mail', 'ftp', 'localhost', 'root',
                'support', 'help', 'info', 'contact', 'about', 'privacy',
                'terms', 'login', 'register', 'signup', 'signin', 'logout',
                'profile', 'settings', 'account', 'dashboard', 'user', 'users'
            ]
            if self.username.lower() in reserved_usernames:
                raise ValidationError({'username': 'This username is reserved and cannot be used.'})

    def save(self, *args, **kwargs):
        # Handle profile picture base64 conversion
        if isinstance(self.profile_picture, str) and self.profile_picture.startswith(
            "data:image"
        ):
            # Split the base64 string and decode it
            format, imgstr = self.profile_picture.split(";base64,")
            ext = format.split("/")[-1]  # Extract the file extension
            image_data = ContentFile(
                base64.b64decode(imgstr), name=f"{uuid.uuid4()}.{ext}"
            )
            self.profile_picture = image_data

        # Auto-generate username if not provided and user has name
        if not self.username and (self.user.first_name or self.user.last_name):
            self.username = generate_unique_username(self)

        # Validate before saving
        self.clean()
        super().save(*args, **kwargs)

    def get_public_url(self):
        """Get the public profile URL using username"""
        if self.username:
            return f"/profile/{self.username}"
        return f"/profile/{self.user.id}"

    def __str__(self):
        return f"{self.user.email} - {self.username or 'No username'}"