from django.db import models
from django.conf import settings
import base64
from django.core.files.base import ContentFile
import uuid

class Profile(models.Model):
    user = models.OneToOneField(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    username = models.Char<PERSON>ield(max_length=100, blank=True, null=True)
    clinic = models.ForeignKey('clinic.Clinic', on_delete=models.SET_NULL, null=True, blank=True)
    bio = models.TextField(blank=True, null=True)   
    profile_picture = models.CharField(max_length=255, blank=True, null=True) 
    private_profile_picture = models.CharField(max_length=255, blank=True, null=True)
    credentials = models.TextField(blank=True, null=True)
    dob = models.CharField(max_length=10, blank=True, null=True)
    genome_tested = models.BooleanField(default=False, blank=True, null=True)
    gender = models.CharField(max_length=10, blank=True, null=True)
    language = models.Char<PERSON>ield(max_length=10, blank=True, null=True)
    blood_group = models.CharField(max_length=10, blank=True, null=True)
    digital_blood = models.CharField(max_length=10, blank=True, null=True)
    address = models.CharField(max_length=255, blank=True, null=True)
    city = models.CharField(max_length=255, blank=True, null=True)
    state = models.CharField(max_length=255, blank=True, null=True)
    zipcode = models.CharField(max_length=20, blank=True, null=True)
    country = models.CharField(max_length=100, blank=True, null=True)
    mobile = models.CharField(max_length=20, blank=True, null=True)
    homephone = models.CharField(max_length=20, blank=True, null=True)
    # New fields for doctor profile
    title = models.CharField(max_length=100, blank=True, null=True)
    education = models.TextField(blank=True, null=True)
    about_me = models.TextField(blank=True, null=True)
    practices = models.TextField(blank=True, null=True)
    affiliations = models.TextField(blank=True, null=True)
    research_papers = models.TextField(blank=True, null=True)
    awards = models.TextField(blank=True, null=True)
    youtube_videos = models.TextField(blank=True, null=True)
    locations = models.TextField(blank=True, null=True)
    speciality = models.CharField(max_length=255, blank=True, null=True)
    is_credentials_verified = models.BooleanField(default=False)
    credential_verification_notes = models.TextField(blank=True, null=True)
    credential_submitted_at = models.DateTimeField(null=True, blank=True)
    preferred_language = models.CharField(max_length=10, choices=settings.LANGUAGES, default=settings.LANGUAGE_CODE) 
    
    show_education = models.BooleanField(default=True)
    show_research_papers = models.BooleanField(default=True)
    show_awards = models.BooleanField(default=True)
    show_youtube_videos = models.BooleanField(default=True)
    show_practice_locations = models.BooleanField(default=True)
    show_credential_documents = models.BooleanField(default=True)
    show_custom_information = models.BooleanField(default=True)
    
    is_public_profile = models.BooleanField(default=False)

    def save(self, *args, **kwargs):
        if isinstance(self.profile_picture, str) and self.profile_picture.startswith(
            "data:image"
        ):
            # Split the base64 string and decode it
            format, imgstr = self.profile_picture.split(";base64,")
            ext = format.split("/")[-1]  # Extract the file extension
            image_data = ContentFile(
                base64.b64decode(imgstr), name=f"{uuid.uuid4()}.{ext}"
            )
            self.profile_picture = image_data

        super().save(*args, **kwargs) 