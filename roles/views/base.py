from datetime import timed<PERSON><PERSON>
from rest_framework import viewsets, status
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import AllowAny, IsAuthenticated
from accounts.serializer import ProfileCategorySerializer
from config.pagination_utils import paginate_queryset
from ..models import CustomPermission, Role, ActivityLog, Profile, Community, Education, ResearchPaper, Award, YouTubeVideo, PracticeLocation, CredentialDocument, CustomInformation
from django.contrib.auth.models import Permission
from accounts.models import CustomUser, ProfileCategory, ProfileCategoryContent
from ..serializers import BulkRoleAssignmentSerializer, RoleSerializer, UserEmailSerializer, UserListSerializer, UserRoleAssignmentSerializer, RoleCreationSerializer, RoleUpdateSerializer, PermissionSerializer, FirstAdminUserSerializer, UserStatusSerializer
from ..permissions import IsAdministrator
from rest_framework import generics, permissions, status
from rest_framework.response import Response
from ..serializers import CommunitySerializer
from roles.permissions import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>dministra<PERSON>, IsAdminOrModerator
from rest_framework import generics, permissions, status
from rest_framework.response import Response
from accounts.models import CustomUser
from ..serializers import UserProfileSerializer, ResearchPaperSerializer, AwardSerializer, YouTubeVideoSerializer, PracticeLocationSerializer
from roles.permissions import IsAdministrator, IsDoctor
from django.db.models import Q
from django.contrib.postgres.search import TrigramSimilarity
from rest_framework.permissions import IsAuthenticated
from rest_framework import viewsets
from rest_framework.permissions import IsAuthenticated
from ..serializers import ResearchPaperSerializer
from rest_framework import viewsets, status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status, permissions
from ..serializers import UserProfileSerializer, EducationSerializer
from rest_framework import generics, permissions, status
from rest_framework.response import Response
from ..serializers import UserProfileAdminSerializer
from roles.permissions import IsAdministrator
from django.shortcuts import get_object_or_404
from rest_framework.parsers import MultiPartParser, FormParser
from django.utils import timezone
from ..serializers import CredentialVerificationSerializer
from roles.send_cred_email import send_credential_verification_email, send_credential_submission_email
from rest_framework.parsers import MultiPartParser, FormParser, JSONParser
from roles.send_cred_email import send_credential_submission_email
from django.conf import settings
from roles.activity_utils import log_activity
from rest_framework.pagination import PageNumberPagination
from rest_framework.response import Response
from django.http import JsonResponse
from roles.serializers import ActivityLogSerializer
from google.cloud import storage
from roles.gcp_utils import get_enterprise_logo_signed_url, upload_file_to_gcs, get_signed_url, get_private_profile_picture_signed_url
from google.oauth2 import service_account
from google.cloud.exceptions import NotFound
from ..serializers import ProfileToggleSerializer
from rest_framework import generics
from rest_framework.permissions import IsAuthenticated
from ..serializers import ProfileFieldsVisibilitySerializer
from rest_framework import serializers
from django.utils.translation import get_language_from_request
from google.cloud import translate_v2 as translate
from roles.compression_helper import compress_image
from roles.translation_utils import TranslationService
from django.db import transaction
import os
import json
import secrets

import logging
from enterprise.models import EnterpriseMember

logger = logging.getLogger(__name__)

class RoleViewSet(viewsets.ModelViewSet):
    queryset = Role.objects.all()
    serializer_class = RoleSerializer
    # permission_classes = [IsAdministrator, IsAuthenticated]

    def get_serializer_class(self):
        if self.action == 'create':
            return RoleCreationSerializer
        elif self.action in ['update', 'partial_update']:
            return RoleUpdateSerializer
        return self.serializer_class

    @action(detail=False, methods=['get'])
    def available_permissions(self, request):
        custom_permissions = CustomPermission.objects.all()
        return Response(PermissionSerializer(custom_permissions, many=True).data)

    @action(detail=False, methods=['post'])
    def assign_role(self, request):
        serializer = UserRoleAssignmentSerializer(data=request.data)
        if serializer.is_valid():
            email = serializer.validated_data['email']
            role_id = serializer.validated_data['role_id']
            
            try:
                user = CustomUser.objects.get(email=email)
                role = Role.objects.get(id=role_id)
                user.role = role
                user.save()
                log_activity(request.user, "Assigned Role", f"User: {user.email}", f"Assigned role: {role.name}")
                return Response({'message': f'Role {role.name} assigned to user {user.email}'}, status=status.HTTP_200_OK)
            except CustomUser.DoesNotExist:
                return Response({'error': 'User not found'}, status=status.HTTP_404_NOT_FOUND)
            except Role.DoesNotExist:
                return Response({'error': 'Role not found'}, status=status.HTTP_404_NOT_FOUND)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'])
    def create_role(self, request):
        serializer = RoleCreationSerializer(data=request.data)
        if serializer.is_valid():
            role = serializer.save()
            return Response(RoleSerializer(role).data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['get'])
    def check_permissions(self, request):
        user = request.user
        if not user.is_authenticated:
            return Response({"error": "User is not authenticated"}, status=status.HTTP_401_UNAUTHORIZED)
        
        # Check if user has a role
        if not user.role:
            return Response({
                "is_administrator": False,
                "is_doctor": False,
                "is_moderator": False,
                "can_create_community": False,
                "can_update_community": False,
                "can_delete_community": False,
                "can_manage_members": False,
                "has_role": False
            })
        
        permissions = {
            "is_administrator": user.role.name == 'Administrator',
            "is_doctor": user.role.name == 'Doctor',
            "is_moderator": user.role.name == 'Moderator',
            "can_create_community": user.role.name in ['Administrator', 'Doctor'],
            "can_update_community": user.role.name in ['Administrator', 'Moderator'],
            "can_delete_community": user.role.name == 'Administrator',
            "can_manage_members": user.role.name in ['Administrator', 'Moderator'],
            "has_role": True
        }
        
        return Response(permissions)

    @action(detail=False, methods=['get'])
    def list_users(self, request):
        """List all users with pagination and filtering"""
        queryset = CustomUser.objects.all()
        
        # Filter by role
        role_name = request.query_params.get('role', None)
        if role_name:
            queryset = queryset.filter(role__name=role_name)
            
        # Search by email
        search = request.query_params.get('search', None)
        if search:
            queryset = queryset.filter(email__icontains=search)
            
        # Pagination
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = UserListSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)
            
        serializer = UserListSerializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['post'])
    def bulk_assign_role(self, request):
        """Assign role to multiple users"""
        serializer = BulkRoleAssignmentSerializer(data=request.data)
        if serializer.is_valid():
            role_id = serializer.validated_data['role_id']
            user_emails = serializer.validated_data['user_emails']
            
            try:
                role = Role.objects.get(id=role_id)
                users = CustomUser.objects.filter(email__in=user_emails)
                
                for user in users:
                    user.role = role
                    user.save()
                    log_activity(request.user, "Bulk Assigned Role", f"User: {user.email}", f"Assigned role: {role.name}")
                
                return Response({
                    'message': f'Role {role.name} assigned to {len(users)} users'
                }, status=status.HTTP_200_OK)
            except Role.DoesNotExist:
                return Response({'error': 'Role not found'}, status=status.HTTP_404_NOT_FOUND)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'])
    def revoke_role(self, request):
        """Revoke role from a user"""
        serializer = UserRoleAssignmentSerializer(data=request.data)
        if serializer.is_valid():
            email = serializer.validated_data['email']
            
            try:
                user = CustomUser.objects.get(email=email)
                old_role = user.role.name if user.role else None
                user.role = None
                user.save()
                log_activity(request.user, "Revoked Role", f"User: {user.email}", f"Revoked role: {old_role}")
                return Response({
                    'message': f'Role revoked from user {user.email}'
                }, status=status.HTTP_200_OK)
            except CustomUser.DoesNotExist:
                return Response({'error': 'User not found'}, status=status.HTTP_404_NOT_FOUND)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'])
    def toggle_user_status(self, request):
        """Enable/disable a user"""
        serializer = UserStatusSerializer(data=request.data)
        if serializer.is_valid():
            email = serializer.validated_data['email']
            is_active = serializer.validated_data['is_active']
            
            try:
                user = CustomUser.objects.get(email=email)
                user.is_active = is_active
                user.save()
                log_activity(request.user, "Toggle User Status", f"User: {user.email}", f"User {is_active}")
                return Response({
                    'message': f'User {user.email} has been {is_active}'
                }, status=status.HTTP_200_OK)
            except CustomUser.DoesNotExist:
                return Response({'error': 'User not found'}, status=status.HTTP_404_NOT_FOUND)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'])
    def reset_user_password(self, request):
        """Reset user's password"""
        serializer = UserEmailSerializer(data=request.data)
        if serializer.is_valid():
            email = serializer.validated_data['email']
            
            try:
                user = CustomUser.objects.get(email=email)
                # Generate a random password
                new_password = CustomUser.objects.make_random_password()
                user.set_password(new_password)
                user.save()
                
                # TODO: Send email with new password
                
                log_activity(request.user, "Reset Password", f"User: {user.email}", "Password reset")
                return Response({
                    'message': f'Password has been reset for user {user.email}'
                }, status=status.HTTP_200_OK)
            except CustomUser.DoesNotExist:
                return Response({'error': 'User not found'}, status=status.HTTP_404_NOT_FOUND)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['delete'])
    def delete_user(self, request):
        """Delete a user"""
        serializer = UserEmailSerializer(data=request.data)
        if serializer.is_valid():
            email = serializer.validated_data['email']
            
            try:
                user = CustomUser.objects.get(email=email)
                user_email = user.email
                user.delete()
                log_activity(request.user, "Delete User", f"User: {user_email}", "User deleted")
                return Response({
                    'message': f'User {user_email} has been deleted'
                }, status=status.HTTP_200_OK)
            except CustomUser.DoesNotExist:
                return Response({'error': 'User not found'}, status=status.HTTP_404_NOT_FOUND)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'])
    def generate_shared_access_token(self, request):
        """
        Generate a token for shared access with specific permissions.
        """
        if not request.user.is_authenticated:
            return Response({"error": "Authentication required"}, status=status.HTTP_401_UNAUTHORIZED)

        # Get the target user email and permissions from the request
        target_email = request.data.get('target_email')
        permissions = request.data.get('permissions', [])
        expires_in_days = request.data.get('expires_in_days', 7)  # Default 7 days

        if not target_email:
            return Response({"error": "Target email is required"}, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Get the target user
            target_user = CustomUser.objects.get(email=target_email)
            
            # Create the access token
            token = AccessToken.objects.create(
                created_by=request.user,
                granted_to=target_user,
                expires_at=timezone.now() + timedelta(days=expires_in_days)
            )

            # Add custom permissions if specified
            if permissions:
                custom_permissions = CustomPermission.objects.filter(codename__in=permissions)
                token.custom_permissions.set(custom_permissions)

            # Create a shared access record
            SharedAccess.objects.create(
                shared_by=request.user.profile,
                shared_with=target_user.profile
            )

            return Response({
                "token": token.token,
                "expires_at": token.expires_at,
                "permissions": [p.codename for p in token.custom_permissions.all()]
            })

        except CustomUser.DoesNotExist:
            return Response({"error": "Target user not found"}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['post'])
    def manage_shared_access(self, request):
        """
        Manage shared access permissions between users.
        """
        try:
            target_email = request.data.get('target_email')
            permissions = request.data.get('permissions', [])
            action = request.data.get('action', 'grant')  # 'grant' or 'revoke'
            
            if not target_email:
                return Response(
                    {'error': 'Target email is required'},
                    status=status.HTTP_400_BAD_REQUEST
                )
                
            try:
                target_user = CustomUser.objects.get(email=target_email)
            except CustomUser.DoesNotExist:
                return Response(
                    {'error': 'Target user not found'},
                    status=status.HTTP_404_NOT_FOUND
                )
                
            if action == 'grant':
                # Create or update shared access
                shared_access, created = SharedAccess.objects.update_or_create(
                    granted_by=request.user,
                    granted_to=target_user,
                    defaults={
                        'permissions': permissions,
                        'is_active': True,
                        'expires_at': timezone.now() + timezone.timedelta(days=7)
                    }
                )
                
                # Generate access token
                access_token = AccessToken.objects.create(
                    token=secrets.token_urlsafe(32),
                    granted_by=request.user,
                    granted_to=target_user,
                    expires_at=shared_access.expires_at
                )
                
                # Add custom permissions
                for permission in permissions:
                    access_token.custom_permissions.add(permission)
                    
                return Response({
                    'message': 'Shared access granted successfully',
                    'access_token': access_token.token,
                    'expires_at': access_token.expires_at
                }, status=status.HTTP_200_OK)
                
            elif action == 'revoke':
                # Revoke shared access
                SharedAccess.objects.filter(
                    granted_by=request.user,
                    granted_to=target_user
                ).update(is_active=False)
                
                # Deactivate access tokens
                AccessToken.objects.filter(
                    granted_by=request.user,
                    granted_to=target_user
                ).update(is_active=False)
                
                return Response({
                    'message': 'Shared access revoked successfully'
                }, status=status.HTTP_200_OK)
                
            else:
                return Response(
                    {'error': 'Invalid action'},
                    status=status.HTTP_400_BAD_REQUEST
                )
                
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class AvailableRolesView(viewsets.ReadOnlyModelViewSet):
    queryset = Role.objects.all()
    serializer_class = RoleSerializer
    permission_classes = [IsAdministrator, IsAuthenticated]

    def list(self, request):
        roles = self.get_queryset()
        return Response({'roles': [{'id': role.id, 'name': role.name} for role in roles]})


class UserProfileView(APIView):
    permission_classes = []
    parser_classes = (MultiPartParser, FormParser, JSONParser)
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.translation_service = TranslationService()
    
    def get_object(self, user):
        try:
            return Profile.objects.get(user=user)
        except Profile.DoesNotExist:
            return Profile.objects.create(user=user)
    
    def get(self, request, pk=None):
        try:
            if pk:
                try:
                    user = CustomUser.objects.select_related('profile').get(id=pk)
                except CustomUser.DoesNotExist:
                    return Response(
                        {"error": "User not found"}, 
                        status=status.HTTP_404_NOT_FOUND
                    )

                profile = self.get_object(user)
                logger.info(f"login user is {request.user}")
                # Access rules:
                # - If profile is public: anyone can view
                # - If profile is not public: only the owner can view
                if profile.is_public_profile:
                    pass  # Public profile, allow access
                else:
                    # Not public: only allow if user is authenticated and is the owner
                    if not request.user.is_authenticated or request.user != profile.user:
                        return Response(
                            {"error": "This profile is private"}, 
                            status=status.HTTP_403_FORBIDDEN
                        )
            else:
                # No pk provided: only allow authenticated users to view their own profile
                if not request.user.is_authenticated:
                    return Response(
                        {"error": "Authentication required to view own profile"}, 
                        status=status.HTTP_401_UNAUTHORIZED
                    )
                profile = self.get_object(request.user)
            
            serializer = UserProfileSerializer(profile)
            data = serializer.data
            
            # Handle profile pictures
            if profile.profile_picture:
                full_blob_name = f"UID_{profile.user.id}/profile_picture/{profile.profile_picture}"
                data['profile_picture'] = get_signed_url(full_blob_name, profile.user.id, timedelta(days=7))
            
            if profile.private_profile_picture and (request.user.is_authenticated and (request.user == profile.user or request.user.role.name == 'Administrator')):
                try:
                    data['private_profile_picture'] = get_private_profile_picture_signed_url(profile.private_profile_picture, profile.user.id)
                except Exception as e:
                    logger.error(f"Error generating signed URL for private profile picture: {str(e)}")
                    data['private_profile_picture'] = None

            try:
                enterprise = None
                # Nếu là owner
                if hasattr(profile.user, "enterprise") and profile.user.enterprise:
                    enterprise = profile.user.enterprise
                    user_is_owner = request.user == enterprise.owner
                else:
                    # Nếu là member
                    member_obj = EnterpriseMember.objects.filter(user=profile.user, is_active=True).select_related("enterprise").first()
                    enterprise = member_obj.enterprise if member_obj else None
                    user_is_owner = False

                user_is_member = False
                if enterprise:
                    user_is_member = EnterpriseMember.objects.filter(enterprise=enterprise, user=request.user, is_active=True).exists()

                if enterprise and enterprise.logo and (user_is_owner or user_is_member):
                    data['enterprise_logo'] = get_enterprise_logo_signed_url(enterprise.logo, profile.user.id)
                else:
                    data['enterprise_logo'] = None
            except Exception as e:
                logger.error(f"Error getting enterprise logo: {str(e)}")
                data['enterprise_logo'] = None
            # Filter data based on visibility settings
            if profile.is_public_profile:
                if not profile.show_education:
                    data.pop('education', None)
                if not profile.show_research_papers:
                    data.pop('research_papers', None)
                if not profile.show_awards:
                    data.pop('awards', None)
                if not profile.show_youtube_videos:
                    data.pop('youtube_videos', None)
                if not profile.show_practice_locations:
                    data.pop('practice_locations', None)
                if not profile.show_credential_documents:
                    data.pop('credential_documents', None)
                if not profile.show_custom_information:
                    data.pop('custom_information', None)

            return Response(data)

        except Exception as e:
            logger.error(f"Error in get method: {str(e)}")
            return Response(
                {"error": str(e)}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['get'])
    def get_profile_categories(self, request, pk=None):
        try:
            page = self.request.query_params.get('page', 1)
            page_size = self.request.query_params.get('page_size', 10)
            category_id = self.request.query_params.get('id', None)
            
            user = CustomUser.objects.select_related('profile').get(id=pk)

            if not user.profile.is_public_profile:
                return Response({"error": "This profile is private"}, status=status.HTTP_403_FORBIDDEN)
            # Get all categories with their related content
            queryset = ProfileCategory.objects.filter(
                user=user,
                hidden=False  # Only get non-hidden categories
            ).prefetch_related(
                'profilecategorycontent_set'  # Get all related content
            ).order_by('created_at')
            
            # Filter by ID if provided
            if category_id:
                queryset = queryset.filter(id=category_id)
            
            # Filter by name if provided
            name = self.request.query_params.get('name', None)
            if name:
                queryset = queryset.filter(name__icontains=name)
                
            paginated_queryset = paginate_queryset(queryset, page, page_size)
            serializer = self.get_serializer(paginated_queryset, many=True)
            
            # Add contents to each category in response
            response_data = serializer.data
            for category in response_data:
                category_contents = ProfileCategoryContent.objects.filter(
                    profile_category_id=category['id']
                ).values('id', 'content')
                category['contents'] = [
                    {'id': content['id'], 'content': content['content']} 
                    for content in category_contents
                ]
                
            return Response(response_data)
            
        except Exception as e:
            logger.error(f"Error listing profile categories: {str(e)}")
            return Response(
                {'error': 'Failed to retrieve profile categories'}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def post(self, request):
        logger.info("Received POST request for UserProfileView")
        profile = self.get_object(request.user)
        serializer = UserProfileSerializer(profile, data=request.data, partial=True)
        if serializer.is_valid():
            if 'profile_picture' in request.FILES:
                file = request.FILES['profile_picture']
                try:
                    compressed_image = compress_image(file)
                    blob_name = upload_file_to_gcs(compressed_image, request.user.id, file_type='profile_picture')
                    profile.profile_picture = blob_name
                    profile.save()
                except Exception as e:
                    logger.error(f"Error uploading file: {str(e)}")
                    return Response({"error": "Failed to upload file"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                
            if 'private_profile_picture' in request.FILES:
                private_file = request.FILES['private_profile_picture']
                try:
                    compressed_private_image = compress_image(private_file)
                    private_blob_name = upload_file_to_gcs(compressed_private_image, request.user.id, file_type='private_profile_picture')
                    profile.private_profile_picture = private_blob_name
                    profile.save()
                except Exception as e:
                    logger.error(f"Error uploading file: {str(e)}") 
                    return Response({"error": "Failed to upload file"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                
            try:
                with transaction.atomic():
                    updated_profile = serializer.save()
                    self.translation_service.invalidate_data(f'user_{profile.user.id}')
                return Response(UserProfileSerializer(updated_profile).data, status=status.HTTP_200_OK)
            except serializers.ValidationError as e:
                return Response(e.detail, status=status.HTTP_400_BAD_REQUEST)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    put = post
    patch = post

    def handle_nested_data(self, data, profile):
        nested_fields = {
            'research_papers': (ResearchPaper, ResearchPaperSerializer),
            'awards': (Award, AwardSerializer),
            'youtube_videos': (YouTubeVideo, YouTubeVideoSerializer),
            'practice_locations': (PracticeLocation, PracticeLocationSerializer),
            'education': (Education, EducationSerializer),
        }

        for field, (model, serializer_class) in nested_fields.items():
            if field in data:
                self.update_nested_data(data[field], model, serializer_class, profile)

    def update_nested_data(self, items_data, model, serializer_class, profile):
        existing_items = model.objects.filter(profile=profile)
        existing_ids = set(existing_items.values_list('id', flat=True))
        updated_ids = set()

        for item_data in items_data:
            item_id = item_data.get('id')
            if item_id:
                if item_id in existing_ids:
                    item = existing_items.get(id=item_id)
                    serializer = serializer_class(item, data=item_data, partial=True)
                else:
                    continue  # Skip if ID is provided but doesn't exist
            else:
                serializer = serializer_class(data=item_data)

            if serializer.is_valid():
                serializer.save(profile=profile)
                if item_id:
                    updated_ids.add(item_id)
            else:
                # You might want to handle validation errors here
                pass

        # Delete items that weren't updated or created
        items_to_delete = existing_ids - updated_ids
        existing_items.filter(id__in=items_to_delete).delete()

    def delete(self, request):
        profile = self.get_object(request.user)
        
        # Get the picture type from the request body
        picture_type = request.data.get('type')
        
        if not picture_type:
            return Response({"error": "Picture type must be specified. Use 'public' or 'private'."}, status=status.HTTP_400_BAD_REQUEST)
        
        if picture_type not in ['public', 'private']:
            return Response({"error": "Invalid picture type. Use 'public' or 'private'."}, status=status.HTTP_400_BAD_REQUEST)
        
        picture_field = 'profile_picture' if picture_type == 'public' else 'private_profile_picture'
        
        if not getattr(profile, picture_field):
            return Response({"message": f"No {picture_type} profile picture to delete"}, status=status.HTTP_404_NOT_FOUND)
        
        try:
            client = storage.Client(project=settings.GS_PROJECT_ID, credentials=settings.GS_CREDENTIALS)
            bucket = client.get_bucket(settings.GS_BUCKET_NAME)
            
            file_name = getattr(profile, picture_field).split('/')[-1]
            folder = 'profile_picture' if picture_type == 'public' else 'private_profile_picture'
            full_blob_name = f"user_{request.user.id}/{folder}/{file_name}"
            
            blob = bucket.blob(full_blob_name)
            
            try:
                blob.delete()
                logger.info(f"Deleted blob: {full_blob_name}")
            except NotFound:
                logger.warning(f"Blob not found: {full_blob_name}")
            
            setattr(profile, picture_field, None)
            profile.save()
            return Response({"message": f"{picture_type.capitalize()} profile picture deleted successfully"}, status=status.HTTP_200_OK)
        except Exception as e:
            logger.error(f"Error deleting {picture_type} profile picture: {str(e)}")
            return Response({"error": f"Failed to delete {picture_type} profile picture"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def get_profile_categories(request, pk=None):
    try:
        page = request.query_params.get('page', 1)
        page_size = request.query_params.get('page_size', 10)
        category_id = request.query_params.get('id', None)
            
        user = CustomUser.objects.select_related('profile').get(id=pk)

        if not user.profile.is_public_profile:
            return Response({"error": "This profile is private"}, status=status.HTTP_403_FORBIDDEN)
        # Get all categories with their related content
        queryset = ProfileCategory.objects.filter(
            user=user,
            hidden=False  # Only get non-hidden categories
        ).prefetch_related(
            'profilecategorycontent_set'  # Get all related content
        ).order_by('created_at')
        
        # Filter by ID if provided
        if category_id:
            queryset = queryset.filter(id=category_id)
        
        # Filter by name if provided
        name = request.query_params.get('name', None)
        if name:
            queryset = queryset.filter(name__icontains=name)
            
        paginated_queryset = paginate_queryset(queryset, page, page_size)
        serializer = ProfileCategorySerializer(paginated_queryset, many=True)
        
        # Add contents to each category in response
        response_data = serializer.data
        for category in response_data:
            category_contents = ProfileCategoryContent.objects.filter(
                profile_category_id=category['id']
            ).values('id', 'content')
            category['contents'] = [
                {'id': content['id'], 'content': content['content']} 
                for content in category_contents
            ]
            
        return Response(response_data)
        
    except Exception as e:
        logger.error(f"Error listing profile categories: {str(e)}")
        return Response(
            {'error': 'Failed to retrieve profile categories'}, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


class ProfileToggleView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        profile = request.user.profile
        serializer = ProfileToggleSerializer(profile)
        return Response(serializer.data)

    def put(self, request):
        profile = request.user.profile
        logger.info(f"Received data: {request.data}")
        logger.info(f"Current is_public_profile value: {profile.is_public_profile}")
        
        serializer = ProfileToggleSerializer(profile, data=request.data, partial=True)
        if serializer.is_valid():
            updated_profile = serializer.save()
            logger.info(f"Updated is_public_profile value: {updated_profile.is_public_profile}")
            return Response(serializer.data)
        logger.error(f"Serializer errors: {serializer.errors}")
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)




class ProfileFieldsVisibilityView(generics.RetrieveUpdateAPIView):
    serializer_class = ProfileFieldsVisibilitySerializer
    permission_classes = [IsAuthenticated]

    def get_object(self):
        return self.request.user.profile

    def update(self, request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)
        return Response(serializer.data)


class UserProfileAdminView(generics.RetrieveUpdateAPIView):
    queryset = Profile.objects.all()
    serializer_class = UserProfileAdminSerializer
    permission_classes = [IsAdministrator, IsAuthenticated]
    lookup_field = 'user__email'  # Use the user's email as the lookup field
    http_method_names = ['get', 'post', 'put', 'patch']  # Add 'post' here

    def get_object(self):
        email = self.kwargs.get('user__email')
        user = get_object_or_404(CustomUser, email=email)
        return get_object_or_404(Profile, user=user)

    def update(self, request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        if getattr(instance, '_prefetched_objects_cache', None):
            # If 'prefetch_related' has been applied to a queryset, we need to
            # forcibly invalidate the prefetch cache on the instance.
            instance._prefetched_objects_cache = {}

        return Response(serializer.data)

    def partial_update(self, request, *args, **kwargs):
        kwargs['partial'] = True
        return self.update(request, *args, **kwargs)

    def post(self, request, *args, **kwargs):
        return self.partial_update(request, *args, **kwargs)

    @action(detail=True, methods=['post'])
    def verify_credentials(self, request, user__email=None):
        profile = self.get_object()
        profile.is_credentials_verified = True
        profile.save()
        serializer = self.get_serializer(profile)
        return Response(serializer.data)
    



class CommunityViewSet(viewsets.ModelViewSet):
    queryset = Community.objects.all()
    serializer_class = CommunitySerializer
    permission_classes = [IsAuthenticated]


    def perform_create(self, serializer):
        serializer.save()
        community = serializer.save(creator=self.request.user)
        log_activity(self.request.user, "Created Community", f"Community: {community.name}")
        
    def perform_update(self, serializer):
        community = serializer.save()
        log_activity(self.request.user, "Updated Community", f"Community: {community.name}")
        
    def perform_destroy(self, instance):
        log_activity(self.request.user, "Deleted Community", f"Community: {instance.name}")
        instance.delete()
        
    def join_community(self, request, pk=None):
        community = self.get_object()
        community.members.add(request.user)
        log_activity(request.user, "Joined Community", f"Community: {community.name}")
        return Response(status=status.HTTP_204_NO_CONTENT)

    def leave_community(self, request, pk=None):
        community = self.get_object()
        community.members.remove(request.user)
        log_activity(request.user, "Left Community", f"Community: {community.name}")
        return Response(status=status.HTTP_204_NO_CONTENT)

    @action(detail=False, methods=['get'], url_path='search')
    def search_communities(self, request):
        query = request.query_params.get("q", "")
        if query:
            # Combine exact, case-insensitive, and similarity searches
            communities = Community.objects.annotate(
                similarity=TrigramSimilarity('name', query) + TrigramSimilarity('description', query)
            ).filter(
                Q(name__iexact=query) |  # Exact match (case-insensitive)
                Q(name__icontains=query) |  # Partial match (case-insensitive)
                Q(description__icontains=query) |  # Partial match in description
                Q(similarity__gt=0.3)  # Similar names or descriptions
            ).order_by('-similarity', 'name')  # Order by similarity, then name

            serializer = self.get_serializer(communities, many=True)
            return Response(serializer.data)
        return Response({"detail": "Please provide a search query."}, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'], permission_classes=[IsAdminOrModerator])
    def flag_post(self, request, pk=None):
        post = self.get_object()
        post.is_flagged = True
        post.save()
        return Response({"message": "Post has been flagged"}, status=status.HTTP_200_OK)

    @action(detail=True, methods=['delete'], permission_classes=[IsAdminOrModerator])
    def delete_post(self, request, pk=None):
        post = self.get_object()
        post.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)



class CommunityCreateView(generics.CreateAPIView):
    queryset = Community.objects.all()
    serializer_class = CommunitySerializer
    permission_classes = [CanCreateCommunity]

    def perform_create(self, serializer):
        serializer.save(creator=self.request.user)
        log_activity(self.request.user, "Created Community", f"Community: {serializer.instance.name}")
        
class CommunityListView(generics.ListAPIView):
    queryset = Community.objects.all()
    serializer_class = CommunitySerializer
    permission_classes = [permissions.IsAuthenticated]

class CommunityDetailView(generics.RetrieveUpdateDestroyAPIView):
    queryset = Community.objects.all()
    serializer_class = CommunitySerializer
    permission_classes = [permissions.IsAuthenticated]

    def update(self, request, *args, **kwargs):
        if not IsAdminOrModerator().has_permission(request, self):
            return Response({"error": "Only Admin or moderators can update communities"}, status=status.HTTP_403_FORBIDDEN)
        return super().update(request, *args, **kwargs)

    def destroy(self, request, *args, **kwargs):
        if not IsAdministrator().has_permission(request, self):
            return Response({"error": "Only Admin can delete communities"}, status=status.HTTP_403_FORBIDDEN)
        return super().destroy(request, *args, **kwargs)

class CommunityMembershipView(generics.UpdateAPIView):
    queryset = Community.objects.all()
    serializer_class = CommunitySerializer
    permission_classes = [IsAdminOrModerator]

    def update(self, request, *args, **kwargs):
        community = self.get_object()
        user_id = request.data.get('user_id')
        action = request.data.get('action')

        if not user_id or not action:
            return Response({"error": "Both user_id and action are required"}, status=status.HTTP_400_BAD_REQUEST)

        try:
            user = CustomUser.objects.get(id=user_id)
        except CustomUser.DoesNotExist:
            return Response({"error": "User not found"}, status=status.HTTP_404_NOT_FOUND)

        if action == 'add':
            community.members.add(user)
        elif action == 'remove':
            community.members.remove(user)
        else:
            return Response({"error": "Invalid action. Use 'add' or 'remove'"}, status=status.HTTP_400_BAD_REQUEST)

        return Response({"message": f"User {action}ed to/from the community successfully"})    
    

class DoctorCredentialSubmissionView(generics.CreateAPIView):
    queryset = Profile.objects.all()
    serializer_class = CredentialVerificationSerializer
    permission_classes = [permissions.IsAuthenticated]
    parser_classes = (MultiPartParser, FormParser)

    def perform_create(self, serializer):
        profile = self.request.user.profile
        instance = serializer.save()
        
        # Get the newly created documents
        documents = CredentialDocument.objects.filter(profile=profile).order_by('-uploaded_at')[:len(serializer.validated_data['credential_documents'])]
        
        # Notify administrators about the new submission
        send_credential_submission_email(profile, documents)

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        headers = self.get_success_headers(serializer.data)
        return Response(serializer.data, status=status.HTTP_201_CREATED, headers=headers)

class AdminCredentialVerificationView(generics.UpdateAPIView):
    queryset = Profile.objects.all()
    serializer_class = CredentialVerificationSerializer
    permission_classes = [IsAdministrator]
    lookup_field = 'user__email'

    def update(self, request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        
        # Update verification status and notes
        instance.is_credentials_verified = serializer.validated_data.get('is_credentials_verified', instance.is_credentials_verified)
        instance.credential_verification_notes = serializer.validated_data.get('credential_verification_notes', instance.credential_verification_notes)
        instance.save()

        # If credentials are verified, send email to the doctor
        if instance.is_credentials_verified:
            send_credential_verification_email(instance.user)

        return Response(serializer.data)
         
class ActivityLogView(generics.ListAPIView):
    queryset = ActivityLog.objects.all().order_by('-timestamp')
    serializer_class = ActivityLogSerializer
    permission_classes = [IsAuthenticated, IsAdministrator]
    pagination_class = PageNumberPagination
    def get_queryset(self):
        queryset = super().get_queryset()
        
        user_email = self.request.query_params.get('user_email')
        action = self.request.query_params.get('action')
        role = self.request.query_params.get('role')
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        if user_email:
            queryset = queryset.filter(user__email=user_email)
        if action:
            queryset = queryset.filter(action__icontains=action)
        if role:
            queryset = queryset.filter(role=role)
        if start_date and end_date:
            queryset = queryset.filter(timestamp__range=[start_date, end_date])
        return queryset
    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        total_logs = ActivityLog.objects.count()
        filtered_logs = queryset.count()
        
        if filtered_logs == 0:
            return JsonResponse({
                "message": "No logs found",
                "total_logs_in_database": total_logs,
                "filters_applied": bool(request.query_params),
                "applied_filters": dict(request.query_params)
            })
        
        return super().list(request, *args, **kwargs)        


from django.utils import translation

class ChangeLanguageView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        language = request.data.get('language')
        if language not in dict(settings.LANGUAGES).keys():
            return JsonResponse({'error': 'Invalid language code'}, status=400)

        translation.activate(language)
        request.session[settings.LANGUAGE_SESSION_KEY] = language
        request.session.modified = True

        return JsonResponse({
            'message': f'Language changed to {language}',
            'source_language': translation.get_language(),
            'session_language': request.session.get(settings.LANGUAGE_SESSION_KEY),
            'activated_language': translation.get_language()
        })

from django.utils import translation


class ChangeLanguageAPIView(APIView):
    def post(self, request):
        lang = request.data.get('lang')
        if lang in dict(settings.LANGUAGES).keys():
            translation.activate(lang)
            request.session[translation.LANGUAGE_SESSION_KEY] = lang
            if request.user.is_authenticated:
                request.user.preferred_language = lang
                request.user.save()
            return Response({"message": f"Language changed to {lang}"}, status=status.HTTP_200_OK)
        return Response({"error": "Invalid language"}, status=status.HTTP_400_BAD_REQUEST)

@api_view(['POST'])
@permission_classes([AllowAny])
def create_first_admin(request):
    """
    Special endpoint to create the first admin user.
    This endpoint should only be used once to create the initial admin user.
    After that, use the regular admin management endpoints.
    """
    # Check if there are any users with admin role
    if CustomUser.objects.filter(role__name='Admin').exists():
        return Response(
            {"error": "Admin users already exist. Use the regular admin management endpoints."},
            status=status.HTTP_400_BAD_REQUEST
        )

    serializer = FirstAdminUserSerializer(data=request.data)
    if serializer.is_valid():
        try:
            # Create the user with email verified
            user = CustomUser.objects.create_user(
                email=serializer.validated_data['email'],
                password=serializer.validated_data['password'],
                first_name=serializer.validated_data.get('first_name', ''),
                last_name=serializer.validated_data.get('last_name', ''),
                is_email_verified=True  # Set email as verified
            )

            # Assign admin role
            admin_role = Role.objects.get(name='Admin')
            user.role = admin_role
            user.save()

            # Log the activity
            log_activity(user, "Created First Admin", f"User: {user.email}", "First admin user created")

            return Response({
                "message": "First admin user created successfully",
                "user": {
                    "email": user.email,
                    "first_name": user.first_name,
                    "last_name": user.last_name
                }
            }, status=status.HTTP_201_CREATED)
        except Role.DoesNotExist:
            return Response(
                {"error": "Admin role not found. Please run migrations and create default roles."},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
        except Exception as e:
            return Response(
                {"error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)