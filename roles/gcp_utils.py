from google.cloud import storage
from django.conf import settings
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)
def upload_file_to_gcs(file, user_id, file_type='profile_picture'):
    try:
        client = storage.Client(project=settings.GS_PROJECT_ID, credentials=settings.GS_CREDENTIALS)
        bucket = client.get_bucket(settings.GS_BUCKET_NAME)
        
        if file_type == 'profile_picture':
            blob_name = f"UID_{user_id}/profile_picture/{file.name}"
        elif file_type == 'private_profile_picture':
            blob_name = f"UID_{user_id}/private_profile_picture/{file.name}"
        elif file_type == 'clinic_logo':
            blob_name = f"UID_{user_id}/clinic_logo/{file.name}"
        elif file_type == 'clinic_image':
            blob_name = f"UID_{user_id}/clinic_images/{file.name}"
        elif file_type == 'enterprise_logo':
            blob_name = f"UID_{user_id}/enterprise_logo/{file.name}"
        elif file_type == 'enterprise_image':
            blob_name = f"UID_{user_id}/enterprise_images/{file.name}"
        else:
            blob_name = f"user_{user_id}/other/{file.name}"
        
        blob = bucket.blob(blob_name)
        
        # Set the content type based on the file's extension
        content_type = file.content_type
        blob.upload_from_file(file, rewind=True, content_type=content_type)
        
        logger.info(f"File uploaded successfully: {blob_name}")
        return blob_name
    except Exception as e:
        logger.error(f"Error uploading file: {str(e)}")
        raise

def get_signed_url(blob_name, user_id, expiration_time=timedelta(minutes=15)):
    client = storage.Client(project=settings.GS_PROJECT_ID, credentials=settings.GS_CREDENTIALS)
    bucket = client.get_bucket(settings.GS_BUCKET_NAME)
    
    # Check if the blob_name already contains the folder structure
    if not blob_name.startswith(f'UID_{user_id}/profile_picture/'):
        full_blob_name = f"UID_{user_id}/profile_picture/{blob_name}"
    else:
        full_blob_name = blob_name
    
    blob = bucket.blob(full_blob_name)
    
    url = blob.generate_signed_url(
        version="v4",
        expiration=expiration_time,
        method="GET",
        response_disposition="inline"
    )
    return url

def get_clinic_image_signed_url(blob_name, user_id):
    client = storage.Client(project=settings.GS_PROJECT_ID, credentials=settings.GS_CREDENTIALS)
    bucket = client.get_bucket(settings.GS_BUCKET_NAME)
    
    if not blob_name.startswith(f'UID_{user_id}/clinic_images/'):
        full_blob_name = f"UID_{user_id}/clinic_images/{blob_name}"
    else:
        full_blob_name = blob_name
    
    blob = bucket.blob(full_blob_name)
    
    url = blob.generate_signed_url(
        version="v4",
        expiration=datetime.utcnow() + timedelta(minutes=15),
        method="GET",
        response_disposition="inline"
    )
    return url

def get_enterprise_image_signed_url(blob_name, user_id):
    client = storage.Client(project=settings.GS_PROJECT_ID, credentials=settings.GS_CREDENTIALS)
    bucket = client.get_bucket(settings.GS_BUCKET_NAME)
    
    if not blob_name.startswith(f'UID_{user_id}/enterprise_images/'):
        full_blob_name = f"UID_{user_id}/enterprise_images/{blob_name}"
    else:
        full_blob_name = blob_name
    
    blob = bucket.blob(full_blob_name)
    
    url = blob.generate_signed_url(
        version="v4",
        expiration=datetime.utcnow() + timedelta(minutes=15),
        method="GET",
        response_disposition="inline"
    )
    return url

def get_clinic_logo_signed_url(blob_name, user_id):
    client = storage.Client(project=settings.GS_PROJECT_ID, credentials=settings.GS_CREDENTIALS)
    bucket = client.get_bucket(settings.GS_BUCKET_NAME)
    
    if not blob_name.startswith(f'UID_{user_id}/clinic_logo/'): 
        full_blob_name = f"UID_{user_id}/clinic_logo/{blob_name}"
    else:
        full_blob_name = blob_name
    
    blob = bucket.blob(full_blob_name)
    
    url = blob.generate_signed_url(
        version="v4",
        expiration=datetime.utcnow() + timedelta(minutes=15),
        method="GET",
        response_disposition="inline"
    )
    return url

def get_enterprise_logo_signed_url(blob_name, user_id):
    client = storage.Client(project=settings.GS_PROJECT_ID, credentials=settings.GS_CREDENTIALS)
    bucket = client.get_bucket(settings.GS_BUCKET_NAME)
    
    # Nếu blob_name đã chứa '/enterprise_logo/' ở bất kỳ vị trí nào thì dùng luôn blob_name
    if '/enterprise_logo/' in blob_name:
        full_blob_name = blob_name
    else:
        full_blob_name = f"UID_{user_id}/enterprise_logo/{blob_name}"
    
    blob = bucket.blob(full_blob_name)
    
    url = blob.generate_signed_url(
        version="v4",
        expiration=datetime.utcnow() + timedelta(minutes=15),
        method="GET",
        response_disposition="inline"
    )
    return url


def get_private_profile_picture_signed_url(blob_name, user_id):
    client = storage.Client(project=settings.GS_PROJECT_ID, credentials=settings.GS_CREDENTIALS)
    bucket = client.get_bucket(settings.GS_BUCKET_NAME)
    
    if not blob_name.startswith(f'UID_{user_id}/private_profile_picture/'):
        full_blob_name = f"UID_{user_id}/private_profile_picture/{blob_name}"
    else:
        full_blob_name = blob_name
    
    blob = bucket.blob(full_blob_name)  
    
    url = blob.generate_signed_url(
        version="v4",
        expiration=timedelta(days=7),
        method="GET",
        response_disposition="inline"
    )
    return url

def get_profile_picture_signed_url(blob_name, user_id):
    client = storage.Client(project=settings.GS_PROJECT_ID, credentials=settings.GS_CREDENTIALS)
    bucket = client.get_bucket(settings.GS_BUCKET_NAME)
    
    if not blob_name.startswith(f'UID_{user_id}/profile_picture/'):
        full_blob_name = f"UID_{user_id}/profile_picture/{blob_name}"
    else:
        full_blob_name = blob_name
    
    blob = bucket.blob(full_blob_name)
    
    url = blob.generate_signed_url(
        version="v4",
        expiration=timedelta(days=7),
        method="GET",
        response_disposition="inline"
    )
    return url

