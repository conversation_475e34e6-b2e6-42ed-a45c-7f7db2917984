from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, IsAdminUser
from django.db.models import Q, F
from content_management.models import InvitationCode
from content_management.api.serializers.invitation_code import (
    InvitationCodeSerializer, 
    InvitationCodeValidationSerializer,
    InvitationCodeBulkCreateSerializer
)
import logging

logger = logging.getLogger(__name__)

class InvitationCodeViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing invitation codes
    CRUD operations for invitation codes
    """
    queryset = InvitationCode.objects.all()
    serializer_class = InvitationCodeSerializer
    permission_classes = [IsAuthenticated]  # TODO: Restrict to admin users
    
    def get_queryset(self):
        """
        Filter invitation codes based on query parameters
        """
        queryset = InvitationCode.objects.all().order_by('-created_at')
        
        # Filter by active status
        is_active = self.request.query_params.get('is_active')
        if is_active is not None:
            is_active_bool = is_active.lower() in ('true', '1', 'yes')
            queryset = queryset.filter(is_active=is_active_bool)
        
        # Filter by validity (not expired and has remaining uses)
        is_valid = self.request.query_params.get('is_valid')
        if is_valid is not None:
            is_valid_bool = is_valid.lower() in ('true', '1', 'yes')
            if is_valid_bool:
                from django.utils import timezone
                now = timezone.now()
                queryset = queryset.filter(
                    is_active=True,
                    current_uses__lt=F('max_uses')
                ).filter(
                    Q(expires_at__isnull=True) | Q(expires_at__gt=now)
                )
            else:
                from django.utils import timezone
                now = timezone.now()
                queryset = queryset.filter(
                    Q(is_active=False) |
                    Q(current_uses__gte=F('max_uses')) |
                    Q(expires_at__lt=now)
                )
        
        # Search by code or description
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(code__icontains=search) | Q(description__icontains=search)
            )
        
        # Filter by created_by
        created_by = self.request.query_params.get('created_by')
        if created_by:
            queryset = queryset.filter(created_by_id=created_by)
        
        return queryset
    
    def get_permissions(self):
        """
        Different permissions for different actions
        """
        if self.action == 'validate':
            # Allow any authenticated user to validate codes
            return [IsAuthenticated()]
        elif self.action in ['create', 'update', 'partial_update', 'destroy', 'bulk_create']:
            # Only admin users can manage codes
            return [IsAdminUser()]
        else:
            # Default to admin for list/retrieve
            return [IsAdminUser()]
    
    def create(self, request, *args, **kwargs):
        """
        Create a new invitation code
        """
        try:
            serializer = self.get_serializer(data=request.data, context={'request': request})
            serializer.is_valid(raise_exception=True)
            invitation_code = serializer.save()
            
            logger.info(f"Invitation code created: {invitation_code.code} by user {request.user.id}")
            
            return Response(
                serializer.data,
                status=status.HTTP_201_CREATED
            )
        except Exception as e:
            logger.error(f"Error creating invitation code: {str(e)}")
            return Response(
                {"error": "Failed to create invitation code"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def update(self, request, *args, **kwargs):
        """
        Update an invitation code
        """
        try:
            instance = self.get_object()
            serializer = self.get_serializer(instance, data=request.data, partial=kwargs.get('partial', False))
            serializer.is_valid(raise_exception=True)
            invitation_code = serializer.save()
            
            logger.info(f"Invitation code updated: {invitation_code.code} by user {request.user.id}")
            
            return Response(serializer.data)
        except Exception as e:
            logger.error(f"Error updating invitation code: {str(e)}")
            return Response(
                {"error": "Failed to update invitation code"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def destroy(self, request, *args, **kwargs):
        """
        Delete an invitation code
        """
        try:
            instance = self.get_object()
            code = instance.code
            instance.delete()
            
            logger.info(f"Invitation code deleted: {code} by user {request.user.id}")
            
            return Response(status=status.HTTP_204_NO_CONTENT)
        except Exception as e:
            logger.error(f"Error deleting invitation code: {str(e)}")
            return Response(
                {"error": "Failed to delete invitation code"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=False, methods=['post'], permission_classes=[IsAuthenticated])
    def validate(self, request):
        """
        Validate an invitation code without using it
        Public endpoint for checking if a code is valid
        """
        try:
            serializer = InvitationCodeValidationSerializer(data=request.data)
            if serializer.is_valid():
                invitation = serializer.get_invitation()
                is_valid, message = invitation.is_valid()
                
                return Response({
                    "valid": is_valid,
                    "message": message,
                    "code": invitation.code,
                    "remaining_uses": max(0, invitation.max_uses - invitation.current_uses),
                    "expires_at": invitation.expires_at
                })
            else:
                return Response(
                    {"valid": False, "errors": serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST
                )
        except Exception as e:
            logger.error(f"Error validating invitation code: {str(e)}")
            return Response(
                {"error": "Failed to validate invitation code"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=False, methods=['post'], permission_classes=[IsAdminUser])
    def bulk_create(self, request):
        """
        Bulk create invitation codes with a common prefix
        """
        try:
            serializer = InvitationCodeBulkCreateSerializer(data=request.data, context={'request': request})
            serializer.is_valid(raise_exception=True)
            created_codes = serializer.save()
            
            logger.info(f"Bulk created {len(created_codes)} invitation codes by user {request.user.id}")
            
            # Return serialized created codes
            response_serializer = InvitationCodeSerializer(created_codes, many=True)
            
            return Response({
                "message": f"Successfully created {len(created_codes)} invitation codes",
                "codes": response_serializer.data
            }, status=status.HTTP_201_CREATED)
            
        except Exception as e:
            logger.error(f"Error bulk creating invitation codes: {str(e)}")
            return Response(
                {"error": "Failed to bulk create invitation codes"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=True, methods=['post'], permission_classes=[IsAdminUser])
    def deactivate(self, request, pk=None):
        """
        Deactivate an invitation code
        """
        try:
            invitation_code = self.get_object()
            invitation_code.is_active = False
            invitation_code.save(update_fields=['is_active', 'updated_at'])
            
            logger.info(f"Invitation code deactivated: {invitation_code.code} by user {request.user.id}")
            
            serializer = self.get_serializer(invitation_code)
            return Response(serializer.data)
            
        except Exception as e:
            logger.error(f"Error deactivating invitation code: {str(e)}")
            return Response(
                {"error": "Failed to deactivate invitation code"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=True, methods=['post'], permission_classes=[IsAdminUser])
    def activate(self, request, pk=None):
        """
        Activate an invitation code
        """
        try:
            invitation_code = self.get_object()
            invitation_code.is_active = True
            invitation_code.save(update_fields=['is_active', 'updated_at'])
            
            logger.info(f"Invitation code activated: {invitation_code.code} by user {request.user.id}")
            
            serializer = self.get_serializer(invitation_code)
            return Response(serializer.data)
            
        except Exception as e:
            logger.error(f"Error activating invitation code: {str(e)}")
            return Response(
                {"error": "Failed to activate invitation code"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=False, methods=['get'], permission_classes=[IsAdminUser])
    def stats(self, request):
        """
        Get statistics about invitation codes
        """
        try:
            from django.utils import timezone
            from django.db.models import Count, Sum
            
            now = timezone.now()
            
            total_codes = InvitationCode.objects.count()
            active_codes = InvitationCode.objects.filter(is_active=True).count()
            expired_codes = InvitationCode.objects.filter(
                expires_at__lt=now
            ).count()
            used_up_codes = InvitationCode.objects.filter(
                current_uses__gte=F('max_uses')
            ).count()
            
            valid_codes = InvitationCode.objects.filter(
                is_active=True,
                current_uses__lt=F('max_uses')
            ).filter(
                Q(expires_at__isnull=True) | Q(expires_at__gt=now)
            ).count()
            
            total_uses = InvitationCode.objects.aggregate(
                total=Sum('current_uses')
            )['total'] or 0
            
            total_possible_uses = InvitationCode.objects.aggregate(
                total=Sum('max_uses')
            )['total'] or 0
            
            return Response({
                "total_codes": total_codes,
                "active_codes": active_codes,
                "valid_codes": valid_codes,
                "expired_codes": expired_codes,
                "used_up_codes": used_up_codes,
                "total_uses": total_uses,
                "total_possible_uses": total_possible_uses,
                "usage_rate": round((total_uses / total_possible_uses * 100), 2) if total_possible_uses > 0 else 0
            })
            
        except Exception as e:
            logger.error(f"Error getting invitation code stats: {str(e)}")
            return Response(
                {"error": "Failed to get stats"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            ) 