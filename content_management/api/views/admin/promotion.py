from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAdminUser
from django.utils import timezone
from django.utils.dateparse import parse_datetime
from django.db.models import Q
from django.shortcuts import get_object_or_404
import logging

from content_management.models.base import ServicePromotion
from content_management.serializers import ServicePromotionSerializer

logger = logging.getLogger(__name__)


class ServicePromotionViewSet(viewsets.ModelViewSet):
    queryset = ServicePromotion.objects.all()
    serializer_class = ServicePromotionSerializer

    def get_object(self):
        """
        Override get_object to use all_objects manager
        """
        queryset = ServicePromotion.all_objects.all()
        lookup_url_kwarg = self.lookup_url_kwarg or self.lookup_field
        filter_kwargs = {self.lookup_field: self.kwargs[lookup_url_kwarg]}
        obj = get_object_or_404(queryset, **filter_kwargs)
        self.check_object_permissions(self.request, obj)
        return obj

    def get_queryset(self):
        # Get filter params
        show_deleted = self.request.query_params.get('show_deleted', 'false').lower() == 'true'
        show_expired = self.request.query_params.get('show_expired', 'false').lower() == 'true'
        show_inactive = self.request.query_params.get('show_inactive', 'false').lower() == 'true'
        show_only_inactive = self.request.query_params.get('show_only_inactive', 'false').lower() == 'true'
        print("show_deleted:", show_deleted)
        print("show_expired:", show_expired)
        print("show_inactive:", show_inactive)
        print("show_only_inactive:", show_only_inactive)
        
        # Get base queryset
        if show_deleted:
            queryset = ServicePromotion.all_objects.all()
        else:
            queryset = ServicePromotion.objects.all()
            
        print("Initial queryset:", queryset)
        
        # Apply additional filters
        now = timezone.now()
        print("Current time:", now)
        
        if show_only_inactive:
            # Show only inactive, expired or deleted promotions
            queryset = queryset.filter(
                Q(is_active=False) |  # Not active
                Q(end_date__lte=now) |  # Expired
                Q(is_deleted=True)  # Deleted
            )
        else:
            # Normal filtering
            if not show_inactive:
                queryset = queryset.filter(is_active=True)
                
            if not show_expired:
                queryset = queryset.filter(end_date__gt=now)
            
        print("Final queryset:", queryset)
        return queryset

    @action(detail=True, methods=['post'])
    def soft_delete(self, request, pk=None):
        promotion = self.get_object()
        promotion.is_deleted = True
        promotion.is_active = False
        promotion.save()
        return Response(self.get_serializer(promotion).data)

    @action(detail=True, methods=['post'])
    def restore(self, request, pk=None):
        promotion = self.get_object()
        promotion.is_deleted = False
        promotion.save()
        return Response(self.get_serializer(promotion).data)
