# Telemedicine Peer-to-Peer Payment Flow Documentation
## Ravid Healthcare Platform

### **Overview**

This document provides a comprehensive step-by-step guide for the telemedicine peer-to-peer payment system, explaining what happens at each stage and how data flows through the models.

### **System Architecture**

The telemedicine payment system consists of three main components:

1. **Payment Profile Management** (`billing.UserPaymentProfile`)
2. **Consultation Profile Management** (`appointments.DoctorConsultationProfile`) 
3. **Payment Processing** (`billing.UserTransfer` + `appointments.Appointment`)

---

## **PHASE 1: DOCTOR SETUP PROCESS**

### **Step 1: Create Stripe Connect Account**

**API Call:**
```http
POST /billing/doctor-payments/setup_payment_account/
Authorization: Bearer <doctor_jwt_token>
Content-Type: application/json

{
  "account_type": "express",
  "country": "US",
  "business_type": "individual"
}
```

**What Happens:**
1. System checks if doctor already has a payment profile
2. Creates new Stripe Express account via Stripe API
3. Generates onboarding link for doctor to complete setup

**Database Changes:**
```sql
-- Creates or updates UserPaymentProfile
INSERT INTO billing_user_payment_profile (
    id, user_id, stripe_account_id, charges_enabled, 
    payouts_enabled, details_submitted, accept_donations,
    donation_message, minimum_donation, suggested_donation_amounts,
    is_verified, verification_date, created_at, updated_at
) VALUES (
    uuid_generate_v4(),
    <doctor_user_id>,
    'acct_stripe_account_id',  -- From Stripe response
    false,                     -- Initially false until onboarding complete
    false,                     -- Initially false until onboarding complete  
    false,                     -- Initially false until onboarding complete
    false,                     -- Default for doctors
    '',                        -- Empty initially
    500,                       -- $5.00 minimum (not used for consultations)
    '[]',                      -- Empty array initially
    false,                     -- Not verified until setup complete
    null,                      -- No verification date yet
    now(),
    now()
);
```

**Response:**
```json
{
  "account_link": "https://connect.stripe.com/express/onboarding/...",
  "setup_type": "doctor_payment_account",
  "message": "Please complete the account setup process"
}
```

### **Step 2: Complete Stripe Onboarding**

**Process:**
Doctor clicks the account_link and completes Stripe onboarding by providing:
- Personal information (name, DOB, SSN)
- Business information (if applicable)
- Bank account details

**Webhook Processing:**
When doctor completes onboarding, Stripe sends webhook to:
```http
POST /billing/webhooks/connect/
```

**Database Updates:**
```sql
-- Updates UserPaymentProfile after successful onboarding
UPDATE billing_user_payment_profile 
SET 
    charges_enabled = true,      -- Can now receive payments
    payouts_enabled = true,      -- Can receive payouts to bank
    details_submitted = true,    -- All required info provided
    is_verified = true,          -- Account verified
    verification_date = now(),   -- Timestamp of verification
    updated_at = now()
WHERE stripe_account_id = 'acct_stripe_account_id';
```

### **Step 3: Add Bank Account (Optional - if not done in onboarding)**

**API Call:**
```http
POST /billing/doctor-payments/add_bank_account/
Authorization: Bearer <doctor_jwt_token>
Content-Type: application/json

{
  "account_holder_name": "Dr. John Smith",
  "account_holder_type": "individual",
  "routing_number": "*********",
  "account_number": "************",
  "country": "US",
  "currency": "usd"
}
```

**What Happens:**
1. Validates bank account information
2. Creates external account in Stripe Connect account
3. Sets as default payout method

**Database Changes:**
No direct database changes - bank account info stored in Stripe only.
System logs the operation for audit purposes.

### **Step 4: Create Consultation Profile**

**API Call:**
```http
POST /appointments/doctor-consultation-profiles/
Authorization: Bearer <doctor_jwt_token>
Content-Type: application/json

{
  "consultation_fee": 7500,
  "consultation_duration": 45,
  "accepts_telemedicine": true,
  "bio": "Experienced family medicine doctor specializing in telemedicine consultations",
  "specializations": ["Family Medicine", "Telemedicine", "Preventive Care"],
  "languages": ["English", "Spanish"],
  "stripe_account_setup": true,
  "available_hours": {
    "monday": {"start": "09:00", "end": "17:00"},
    "tuesday": {"start": "09:00", "end": "17:00"},
    "wednesday": {"start": "09:00", "end": "17:00"},
    "thursday": {"start": "09:00", "end": "17:00"},
    "friday": {"start": "09:00", "end": "17:00"}
  }
}
```

**Database Changes:**
```sql
-- Creates DoctorConsultationProfile
INSERT INTO appointments_doctorconsultationprofile (
    id, user_id, consultation_fee, consultation_duration,
    accepts_telemedicine, available_hours, bio, specializations,
    languages, is_active, stripe_account_setup, created_at, updated_at
) VALUES (
    uuid_generate_v4(),
    <doctor_user_id>,
    7500,                      -- $75.00 consultation fee in cents
    45,                        -- 45 minutes duration
    true,                      -- Accepts telemedicine
    '{"monday": {"start": "09:00", "end": "17:00"}, ...}',  -- JSON schedule
    'Experienced family medicine doctor...',  -- Bio text
    '["Family Medicine", "Telemedicine", "Preventive Care"]',  -- JSON array
    '["English", "Spanish"]',  -- JSON array
    true,                      -- Profile is active
    true,                      -- Stripe setup completed
    now(),
    now()
);
```

**Validation Logic:**
```python
def is_available_for_telemedicine(self):
    """Check if doctor is available for telemedicine"""
    return (
        self.accepts_telemedicine and 
        self.is_active and 
        self.stripe_account_setup
    )
```

---

## **PHASE 2: PATIENT BOOKING AND PAYMENT PROCESS**

### **Step 5: Patient Creates Appointment**

**API Call:**
```http
POST /appointments/
Authorization: Bearer <patient_jwt_token>
Content-Type: application/json

{
  "doctor": "<doctor_user_id>",
  "start_time": "2025-01-20T14:00:00Z",
  "end_time": "2025-01-20T14:45:00Z",
  "appointment_type": "booking",
  "mode": "video_call",
  "direct_payment": true,
  "notes": "Follow-up consultation for hypertension"
}
```

**Database Changes:**
```sql
-- Creates Appointment record
INSERT INTO appointments_appointment (
    id, patient_id, doctor_id, title, start_time, end_time,
    status, notes, qr_code, google_event_id, location,
    is_all_day, mode, appointment_type, direct_payment,
    insurance, consultation_service_id, payment_transfer_id,
    payment_status, consultation_fee, created_at, updated_at
) VALUES (
    uuid_generate_v4(),
    <patient_user_id>,
    <doctor_user_id>,
    'Appointment with Dr. John Smith',  -- Auto-generated title
    '2025-01-20 14:00:00+00',
    '2025-01-20 14:45:00+00',
    'pending',                 -- Initial status
    'Follow-up consultation for hypertension',
    null,                      -- QR code generated later
    null,                      -- Google event ID created after payment
    'Google Meet',             -- Virtual location
    false,                     -- Not all-day
    'video_call',              -- Video consultation
    'booking',                 -- Doctor booking type
    true,                      -- Direct payment required
    null,                      -- No insurance for direct payment
    null,                      -- No consultation service package
    null,                      -- Payment transfer created in next step
    'pending',                 -- Payment pending
    7500,                      -- Consultation fee from doctor profile
    now(),
    now()
);
```

### **Step 6: Process Consultation Payment**

**API Call:**
```http
POST /appointments/<appointment_id>/process_payment/
Authorization: Bearer <patient_jwt_token>
Content-Type: application/json

{
  "payment_method": "pm_card_visa",
  "save_payment_method": false
}
```

**What Happens:**
1. System retrieves doctor's consultation profile
2. Validates doctor can receive telemedicine payments
3. Calculates platform fee (3% of consultation fee)
4. Creates Stripe Payment Intent
5. Creates UserTransfer record
6. Links transfer to appointment

**Database Changes:**
```sql
-- Creates UserTransfer record
INSERT INTO billing_user_transfer (
    id, sender_id, receiver_id, message, transfer_type,
    platform_fee_id, platform_fee_amount, stripe_payment_intent_id,
    status, amount, currency, created_at, updated_at
) VALUES (
    uuid_generate_v4(),
    <patient_user_id>,
    <doctor_user_id>,
    'Telemedicine consultation - <appointment_id>',
    'payment',                 -- Transfer type
    <platform_fee_id>,         -- Reference to PlatformFee record
    225,                       -- 3% of $75.00 = $2.25 = 225 cents
    'pi_stripe_payment_intent_id',  -- From Stripe Payment Intent
    'pending',                 -- Initial status
    7500,                      -- $75.00 in cents
    'usd',
    now(),
    now()
);

-- Updates Appointment with payment transfer link
UPDATE appointments_appointment
SET
    payment_transfer_id = <transfer_id>,
    payment_status = 'pending',
    consultation_fee = 7500,   -- Store fee at time of booking
    updated_at = now()
WHERE id = <appointment_id>;
```

**Stripe Payment Intent Creation:**
```python
# In UserTransferService.create_transfer()
payment_intent = stripe.PaymentIntent.create(
    amount=7500,  # Total amount
    currency='usd',
    application_fee_amount=225,  # Platform fee
    transfer_data={
        'destination': 'acct_doctor_stripe_account_id',
        'amount': 7275  # Net amount to doctor (7500 - 225)
    },
    metadata={
        'transfer_id': str(transfer.id),
        'appointment_id': str(appointment.id),
        'sender_user_id': str(patient.id),
        'receiver_user_id': str(doctor.id)
    }
)
```

### **Step 7: Patient Completes Payment**

**Frontend Process:**
Patient enters payment details and confirms payment using Stripe Elements.

**Webhook Processing:**
When payment succeeds, Stripe sends webhook:
```http
POST /billing/webhook/
```

**Database Updates:**
```sql
-- Updates UserTransfer status
UPDATE billing_user_transfer
SET
    status = 'completed',
    updated_at = now()
WHERE stripe_payment_intent_id = 'pi_stripe_payment_intent_id';

-- Updates Appointment status and payment
UPDATE appointments_appointment
SET
    payment_status = 'paid',   -- Payment completed
    updated_at = now()
WHERE payment_transfer_id = <transfer_id>;
```

### **Step 8: Doctor Confirms Appointment**

**API Call:**
```http
PUT /appointments/<appointment_id>/
Authorization: Bearer <doctor_jwt_token>
Content-Type: application/json

{
  "status": "confirmed"
}
```

**What Happens:**
1. System validates payment status
2. Creates Google Calendar event
3. Generates Google Meet link
4. Updates appointment record
5. Sends confirmation emails

**Database Changes:**
```sql
-- Updates Appointment with Google Meet details and status
UPDATE appointments_appointment
SET
    status = 'confirmed',
    google_event_id = 'google_calendar_event_id',
    location = 'https://meet.google.com/abc-defg-hij',  -- Google Meet link
    qr_code = 'qr_code_for_meet_link',  -- QR code for easy access
    updated_at = now()
WHERE id = <appointment_id>;
```

### **Step 9: Email Notifications**

**Automatic Process:**
System sends confirmation emails to both patient and doctor using the appointment_confirmation.html template.

**Email Content Includes:**
- Appointment details (date, time, duration)
- Doctor information
- Google Meet link
- Payment confirmation
- Cancellation policy

---

## **PHASE 3: APPOINTMENT MANAGEMENT**

### **Step 10: Doctor Receives Payment**

**Stripe Processing:**
- Platform fee (3%) goes to platform Stripe account
- Net amount (97%) goes to doctor's Stripe Connect account
- Automatic payout to doctor's bank account (daily/weekly based on settings)

**Payment Breakdown:**
```
Total Payment: $75.00
Platform Fee: $2.25 (3%)
Doctor Receives: $72.75 (97%)
```

### **Step 11: Appointment Status Updates**

**During Appointment:**
```sql
-- Doctor can update appointment status
UPDATE appointments_appointment
SET
    status = 'in_progress',
    updated_at = now()
WHERE id = <appointment_id>;
```

**After Appointment:**
```sql
-- Mark appointment as completed
UPDATE appointments_appointment
SET
    status = 'completed',
    updated_at = now()
WHERE id = <appointment_id>;
```

---

## **PHASE 4: REFUND SCENARIOS**

### **Step 12: Appointment Cancellation with Refund**

**API Call:**
```http
POST /appointments/<appointment_id>/cancel/
Authorization: Bearer <patient_or_doctor_jwt_token>
Content-Type: application/json

{
  "reason": "Doctor unavailable due to emergency",
  "refund_requested": true
}
```

**Database Changes:**
```sql
-- Updates Appointment status
UPDATE appointments_appointment
SET
    status = 'cancelled',
    cancellation_reason = 'Doctor unavailable due to emergency',
    updated_at = now()
WHERE id = <appointment_id>;

-- Updates UserTransfer status
UPDATE billing_user_transfer
SET
    status = 'refunded',
    updated_at = now()
WHERE id = <transfer_id>;

-- Updates payment status
UPDATE appointments_appointment
SET
    payment_status = 'refunded',
    updated_at = now()
WHERE id = <appointment_id>;
```

**Stripe Refund Processing:**
```python
# In TelemedicinePaymentService.refund_consultation_payment()
stripe.Refund.create(
    payment_intent='pi_stripe_payment_intent_id',
    amount=7500,  # Full refund amount
    reason='requested_by_customer',
    metadata={
        'appointment_id': str(appointment.id),
        'refund_reason': 'Doctor unavailable due to emergency'
    }
)
```

---

## **MODEL FIELD EXPLANATIONS**

### **UserPaymentProfile Fields**

| Field | Purpose | Values | Example |
|-------|---------|---------|---------|
| `stripe_account_id` | Stripe Connect account identifier | `acct_1234567890` | `acct_1OvKI2eZvKYlo2C0` |
| `charges_enabled` | Can receive payments | `true`/`false` | `true` |
| `payouts_enabled` | Can receive payouts to bank | `true`/`false` | `true` |
| `details_submitted` | Completed onboarding | `true`/`false` | `true` |
| `is_verified` | Account verified by Stripe | `true`/`false` | `true` |
| `accept_donations` | Accepts donation payments | Usually `false` for doctors | `false` |
| `verification_date` | When account was verified | Timestamp | `2025-01-15 10:30:00` |

### **DoctorConsultationProfile Fields**

| Field | Purpose | Values | Example |
|-------|---------|---------|---------|
| `consultation_fee` | Fee in cents | `500` - `50000` | `7500` = $75.00 |
| `consultation_duration` | Duration in minutes | `15` - `180` | `45` minutes |
| `accepts_telemedicine` | Offers video consultations | `true`/`false` | `true` |
| `stripe_account_setup` | Payment setup complete | `true`/`false` | `true` |
| `specializations` | Medical specialties | JSON array | `["Family Medicine", "Cardiology"]` |
| `languages` | Spoken languages | JSON array | `["English", "Spanish"]` |
| `available_hours` | Schedule configuration | JSON object | `{"monday": {"start": "09:00", "end": "17:00"}}` |
| `bio` | Professional description | Text (max 1000 chars) | `"Experienced family doctor..."` |

### **Appointment Fields**

| Field | Purpose | Values | Example |
|-------|---------|---------|---------|
| `appointment_type` | Type of appointment | `booking`/`personal` | `booking` for doctor appointments |
| `mode` | Consultation method | `video_call`/`in_person`/`phone` | `video_call` for telemedicine |
| `direct_payment` | Requires payment | `true`/`false` | `true` for paid consultations |
| `payment_transfer_id` | Link to payment | Foreign key to UserTransfer | UUID reference |
| `payment_status` | Payment state | `pending`/`paid`/`failed`/`refunded` | `paid` |
| `consultation_fee` | Fee at booking time | Stored in cents | `7500` = $75.00 |
| `status` | Appointment state | `pending`/`confirmed`/`completed`/`cancelled` | `confirmed` |
| `google_event_id` | Google Calendar event | String | `google_calendar_event_123` |
| `location` | Meeting location | URL or address | `https://meet.google.com/abc-defg-hij` |

### **UserTransfer Fields**

| Field | Purpose | Values | Example |
|-------|---------|---------|---------|
| `transfer_type` | Type of transfer | `payment`/`donation`/`transfer` | `payment` for consultations |
| `amount` | Total amount in cents | Integer | `7500` = $75.00 |
| `platform_fee_amount` | Platform fee in cents | Integer | `225` = $2.25 (3%) |
| `stripe_payment_intent_id` | Stripe payment reference | String | `pi_1OvKI2eZvKYlo2C0` |
| `status` | Transfer status | `pending`/`completed`/`failed`/`refunded` | `completed` |
| `sender_id` | Patient user ID | Foreign key | UUID reference |
| `receiver_id` | Doctor user ID | Foreign key | UUID reference |
| `message` | Transfer description | Text | `"Telemedicine consultation - <appointment_id>"` |

---

## **VALIDATION RULES**

### **Doctor Availability Validation**
```python
def is_available_for_telemedicine(self):
    """Check if doctor is available for telemedicine"""
    return (
        self.accepts_telemedicine and
        self.is_active and
        self.stripe_account_setup and
        self.user.payment_profile.can_receive_payments()
    )
```

### **Payment Validation**
```python
def is_payment_required(self):
    """Check if payment is required for this appointment"""
    return (
        self.appointment_type == 'booking' and
        self.direct_payment and
        self.mode == 'video_call'
    )
```

### **Fee Validation Rules**
- **Minimum consultation fee**: $5.00 (500 cents)
- **Maximum consultation fee**: $500.00 (50000 cents)
- **Platform fee**: 3% of consultation fee
- **Duration range**: 15-180 minutes
- **Currency**: USD only

### **Business Logic Validation**
```python
def clean(self):
    """Validate transfer data"""
    super().clean()

    # Ensure sender and receiver are different
    if self.sender == self.receiver:
        raise ValidationError("Sender and receiver cannot be the same user")

    # Validate receiver can accept the transfer type
    if self.transfer_type == 'donation':
        receiver_profile = getattr(self.receiver, 'payment_profile', None)
        if not receiver_profile or not receiver_profile.can_receive_donations():
            raise ValidationError("Receiver cannot accept donations")
```

---

## **ERROR HANDLING**

### **Common Error Scenarios**

1. **Doctor Not Setup**:
   - Error: `ValidationError("Doctor consultation profile not found")`
   - Solution: Doctor must create consultation profile first

2. **Payment Profile Missing**:
   - Error: `ValidationError("Doctor payment profile not setup")`
   - Solution: Doctor must complete Stripe Connect onboarding

3. **Stripe Account Issues**:
   - Error: `PaymentError("Failed to process consultation payment")`
   - Solution: Check Stripe account status and retry

4. **Invalid Fee Range**:
   - Error: `ValidationError("Consultation fee must be between $5 and $500")`
   - Solution: Update consultation fee within valid range

5. **Insufficient Permissions**:
   - Error: `PermissionDeniedError("Only doctors can access payment account features")`
   - Solution: Verify user role and permissions

### **Recovery Mechanisms**

- **Automatic retry** for failed payments (up to 3 attempts)
- **Webhook replay** for missed Stripe events
- **Manual reconciliation tools** for payment discrepancies
- **Refund processing** for cancelled appointments
- **Email notifications** for all payment status changes

### **Monitoring and Alerts**

- Payment failure rate monitoring
- Webhook delivery tracking
- Account setup completion rates
- Revenue and fee collection tracking
- Fraud detection and prevention

---

## **SECURITY CONSIDERATIONS**

### **Data Protection**
- All payment data encrypted in transit and at rest
- PCI DSS compliance through Stripe
- HIPAA compliance for healthcare data
- Audit logs for all payment operations

### **Access Control**
- Role-based permissions (doctor/patient/admin)
- JWT token authentication
- API rate limiting
- Stripe webhook signature verification

### **Financial Security**
- Platform fee validation
- Payment amount verification
- Refund authorization controls
- Bank account validation

This comprehensive flow ensures secure, reliable peer-to-peer payments for telemedicine consultations while maintaining full audit trails and compliance with healthcare payment regulations.
