from django.db import models
from django.core.exceptions import ValidationError
from accounts.models import CustomUser
from appointments.models.availability import DoctorAvailability
from appointments.models.override import DoctorAvailabilityOverride
from clinic.models import Clinic
from enterprise.models import Enterprise
from config.models import BaseModel
from upload.models import UploadedFile
from ..constants import (
    APPOINTMENT_STATUS_CHOICES,
    APPOINTMENT_TYPE_CHOICES,
    APPOINTMENT_MODE_CHOICES
)

class Appointment(BaseModel):
    """
    Store appointment information: manual events or doctor bookings.
    """
    creator = models.ForeignKey(
        CustomUser,
        on_delete=models.CASCADE,
        related_name='created_appointments',
        help_text="User who created this appointment"
    )
    patient = models.ForeignKey(
        CustomUser,
        on_delete=models.CASCADE,
        related_name='appointments_as_patient',
        null=True,
        blank=True,
        help_text="Patient (same as creator for manual, different for booking)"
    )
    doctor = models.ForeignKey(
        CustomUser,
        on_delete=models.CASCADE,
        related_name='appointments_as_doctor',
        null=True,
        blank=True,
        limit_choices_to={'role__name': 'doctor'},
        help_text="Doctor (null for manual events)"
    )
    clinic = models.ForeignKey(
        Clinic,
        on_delete=models.CASCADE,
        related_name='appointments',
        null=True,
        blank=True
    )
    enterprise = models.ForeignKey(
        Enterprise,
        on_delete=models.CASCADE,
        related_name='appointments',
        null=True,
        blank=True
    )
    title = models.CharField(
        max_length=255,
        blank=True,
        help_text="Title of the event (e.g., 'Check-up' or 'Personal Meeting')"
    )
    appointment_type = models.CharField(
        max_length=20,
        choices=APPOINTMENT_TYPE_CHOICES,
        default='manual'
    )
    start_time = models.DateTimeField(null=False)
    end_time = models.DateTimeField(null=False)
    status = models.CharField(max_length=20, choices=APPOINTMENT_STATUS_CHOICES, default='pending')
    notes = models.TextField(blank=True)
    qr_code = models.CharField(max_length=255, blank=True, null=True)
    google_event_id = models.CharField(max_length=255, blank=True, null=True)
    location = models.CharField(max_length=255, blank=True, help_text="Physical or virtual location")
    is_all_day = models.BooleanField(default=False, help_text="All-day event flag")
    mode = models.CharField(
        max_length=20,
        choices=APPOINTMENT_MODE_CHOICES,
        default='in_person'
    )
    meeting_link = models.URLField(blank=True, null=True, help_text="Video call link if applicable")
    reminder_sent = models.BooleanField(default=False)
    cancellation_reason = models.TextField(blank=True)
    cancelled_by = models.ForeignKey(
        CustomUser,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='cancelled_appointments'
    )
    slot_reference = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        help_text="Reference to the specific availability slot or override (e.g., 'availability:123' or 'override:456')"
    )
    insurance = models.BooleanField(default=False, help_text="Whether this booking uses insurance (booking only)")
    direct_payment = models.BooleanField(default=False, help_text="Whether this booking is direct payment (booking only)")

    # Consultation service integration (future-ready)
    consultation_service = models.ForeignKey(
        'content_management.Service',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        limit_choices_to={'service_type': 'CONSULTATION'},
        help_text="Selected consultation service package (future use)"
    )

    # Payment tracking for direct payment appointments
    payment_transfer = models.ForeignKey(
        'billing.UserTransfer',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='appointments',
        help_text="Associated payment transfer for direct payment appointments"
    )
    payment_status = models.CharField(
        max_length=20,
        choices=[
            ('pending', 'Pending'),
            ('paid', 'Paid'),
            ('failed', 'Failed'),
            ('refunded', 'Refunded'),
        ],
        default='pending',
        help_text="Payment status for direct payment appointments"
    )
    consultation_fee = models.IntegerField(
        null=True,
        blank=True,
        help_text="Consultation fee in cents for direct payment appointments"
    )
    
    def clean(self):
        if self.start_time >= self.end_time:
            raise ValidationError("End time must be after start time.")
        if self.clinic and self.enterprise:
            raise ValidationError("Appointment cannot belong to both clinic and enterprise.")

        # Validate payment method exclusivity for booking appointments
        if self.appointment_type == 'booking' and self.insurance and self.direct_payment:
            raise ValidationError("Appointment cannot use both insurance and direct payment simultaneously. Please choose one payment method.")

        # Validation for booking with doctor
        if self.appointment_type == 'booking':
            if not self.doctor:
                raise ValidationError("Doctor is required for booking appointments.")
            if not self.patient:
                raise ValidationError("Patient is required for booking appointments.")

            date = self.start_time.date()
            start_time = self.start_time.time()
            end_time = self.end_time.time()
            
            # Check overrides first
            override = DoctorAvailabilityOverride.objects.filter(
                doctor=self.doctor,
                date=date,
                is_active=True
            ).first()

            if override:
                valid_slot = override.time_slots.filter(
                    start_time__lte=start_time,
                    end_time__gte=end_time
                ).exists()
                if not valid_slot:
                    raise ValidationError("Selected time is not within doctor's override slots.")
            else:
                # Check regular availability using get_occurrences
                availabilities = DoctorAvailability.objects.filter(
                    doctor=self.doctor,
                    is_active=True
                ).filter(
                    models.Q(
                        # Non-recurring availabilities
                        recurrence_type='none',
                        start_date=date
                    ) |
                    models.Q(
                        # Recurring availabilities
                        ~models.Q(recurrence_type='none'),
                        start_date__lte=date
                    )
                )

                # Check if any availability has a slot that matches the appointment time
                valid_slot = False
                for availability in availabilities:
                    occurrences = availability.get_occurrences(date, date)
                    for occurrence in occurrences:
                        if (occurrence['start_time'] <= start_time and 
                            occurrence['end_time'] >= end_time):
                            valid_slot = True
                            break
                    if valid_slot:
                        break

                if not valid_slot:
                    raise ValidationError("Selected time is not within doctor's available slots.")

            # Check for conflicting appointments
            conflicting_appointments = Appointment.objects.filter(
                doctor=self.doctor,
                start_time__lt=self.end_time,
                end_time__gt=self.start_time,
                status__in=['pending', 'confirmed']
            ).exclude(id=self.id)
            if conflicting_appointments.exists():
                raise ValidationError("This slot is already booked.")

        # Validation for manual events
        if self.appointment_type == 'manual' and self.doctor:
            raise ValidationError("Manual events cannot have a doctor assigned.")

        # # Add validation for appointment mode
        # if self.mode == 'video_call' and not self.meeting_link:
        #     raise ValidationError("Meeting link is required for video call appointments.")
            
        # Ensure appointment is within clinic/enterprise operating hours if applicable
        if self.clinic:
            # Add validation for clinic hours
            pass
        elif self.enterprise:
            # Add validation for enterprise hours
            pass

    def save(self, *args, **kwargs):
        self.clean()
        if not self.title:
            if self.appointment_type == 'booking':
                self.title = f"Appointment with {self.doctor.get_full_name()}"
            else:
                self.title = "Personal Event"
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.title} - {self.start_time}"

    def get_consultation_fee_usd(self):
        """Get consultation fee in USD (from cents)"""
        return self.consultation_fee / 100 if self.consultation_fee else 0

    def is_payment_required(self):
        """Check if payment is required for this appointment"""
        return (
            self.appointment_type == 'booking' and
            self.direct_payment and
            self.mode == 'video_call'
        )

    def is_paid(self):
        """Check if appointment is paid"""
        return self.payment_status == 'paid'

    def can_be_confirmed_by_doctor(self):
        """Check if doctor can confirm this appointment"""
        if not self.is_payment_required():
            return True  # No payment required, can confirm
        return self.is_paid()  # Payment required, must be paid

class AppointmentAttachment(BaseModel):
    """
    Store file attachments for appointments.
    """
    appointment = models.ForeignKey(
        Appointment,
        on_delete=models.CASCADE,
        related_name='attachments',
        help_text="Appointment this file is attached to"
    )
    file = models.ForeignKey(
        UploadedFile,
        on_delete=models.CASCADE,
        related_name='appointment_attachments',
        help_text="Uploaded file"
    )
    description = models.CharField(
        max_length=255,
        blank=True,
        help_text="Optional description of the attachment"
    )
    
    def __str__(self):
        return f"{self.appointment.title} - {self.file.filename}" 