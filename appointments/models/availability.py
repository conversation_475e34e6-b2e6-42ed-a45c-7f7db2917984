from django.db import models
from django.core.exceptions import ValidationError
from accounts.models import CustomUser
from clinic.models import Clinic
from enterprise.models import Enterprise
from config.models import BaseModel
from ..constants import DAYS_OF_WEEK, RECURRENCE_TYPE_CHOICES, APPOINTMENT_MODE_CHOICES
from datetime import datetime, timedelta

class DoctorAvailability(BaseModel):
    """
    Store availability of doctor with flexible scheduling options
    """
    doctor = models.ForeignKey(
        CustomUser,
        on_delete=models.CASCADE,
        related_name='availabilities',
    )
    clinic = models.ForeignKey(
        Clinic,
        on_delete=models.CASCADE,
        related_name='doctor_availabilities',
        null=True,
        blank=True
    )
    enterprise = models.ForeignKey(
        Enterprise,
        on_delete=models.CASCADE,
        related_name='doctor_availabilities',
        null=True,
        blank=True
    )
    title = models.CharField(max_length=255, blank=True, help_text="Title of the availability")
    start_date = models.DateField(help_text="Start date of availability", null=True, blank=True)
    end_date = models.DateField(null=True, blank=True, help_text="End date of availability (null for indefinite)")
    start_time = models.TimeField(help_text="Start time of availability", null=True, blank=True)
    end_time = models.TimeField(help_text="End time of availability", null=True, blank=True)
    is_active = models.BooleanField(default=True)
    mode = models.CharField(
        max_length=100,
        default='in_person',
        help_text="Comma-separated list of appointment modes (e.g., 'video_call,in_person')"
    )
    
    # Recurrence options
    recurrence_type = models.CharField(
        max_length=20,
        choices=RECURRENCE_TYPE_CHOICES,
        default='none'
    )
    recurrence_interval = models.PositiveIntegerField(default=1, help_text="Interval for recurrence (e.g., every 2 weeks)")
    recurrence_days = models.CharField(max_length=50, blank=True, help_text="Comma-separated days for weekly recurrence (e.g., 'monday,wednesday,friday')")
    recurrence_month_day = models.PositiveIntegerField(null=True, blank=True, help_text="Day of month for monthly recurrence")
    recurrence_end_date = models.DateField(null=True, blank=True, help_text="End date for recurrence")
    recurrence_count = models.PositiveIntegerField(null=True, blank=True, help_text="Number of occurrences for recurrence")

    class Meta:
        ordering = ['start_date', 'start_time']
        unique_together = [
            ('doctor', 'start_date', 'start_time', 'end_time', 'recurrence_type')
        ]

    def clean(self):
        if self.clinic and self.enterprise:
            raise ValidationError("Doctor cannot belong to both clinic and enterprise.")
        
        if self.start_time >= self.end_time:
            raise ValidationError("End time must be after start time.")
            
        if self.end_date and self.start_date > self.end_date:
            raise ValidationError("End date must be after start date.")
            
        if self.recurrence_type != 'none':
            if self.recurrence_type == 'weekly' and not self.recurrence_days:
                raise ValidationError("Weekly recurrence requires recurrence_days to be set.")
            if self.recurrence_type == 'monthly' and not self.recurrence_month_day:
                raise ValidationError("Monthly recurrence requires recurrence_month_day to be set.")
            if not self.recurrence_end_date and not self.recurrence_count:
                raise ValidationError("Recurrence must have either an end date or count.")

    def get_occurrences(self, start_date, end_date):
        """Get all occurrences of this availability within the given date range"""
        if not self.is_active:
            return []

        occurrences = []
        
        # For non-recurring availabilities, only return if the date matches start_date
        if self.recurrence_type == 'none':
            if start_date <= self.start_date <= end_date:
                occurrences.append({
                    'date': self.start_date,
                    'start_time': self.start_time,
                    'end_time': self.end_time
                })
            return occurrences

        # For recurring availabilities
        current_date = self.start_date
        
        # Determine the end date for recurrence
        recurrence_end = None
        if self.recurrence_end_date:
            # Priority to recurrence_end_date if set
            recurrence_end = min(end_date, self.recurrence_end_date)
        elif self.recurrence_count:
            # If no recurrence_end_date but has count, calculate end date based on count
            if self.recurrence_type == 'daily':
                max_date = self.start_date + timedelta(days=(self.recurrence_count - 1) * self.recurrence_interval)
            elif self.recurrence_type == 'weekly':
                max_date = self.start_date + timedelta(weeks=(self.recurrence_count - 1) * self.recurrence_interval)
            elif self.recurrence_type == 'monthly':
                # Add months by replacing month/year values
                max_date = self.start_date
                months_to_add = (self.recurrence_count - 1) * self.recurrence_interval
                year_increment = (max_date.month + months_to_add - 1) // 12
                new_month = ((max_date.month + months_to_add - 1) % 12) + 1
                max_date = max_date.replace(year=max_date.year + year_increment, month=new_month)
            else:  # yearly
                max_date = self.start_date.replace(year=self.start_date.year + (self.recurrence_count - 1) * self.recurrence_interval)
            recurrence_end = min(end_date, max_date)
        else:
            recurrence_end = end_date

        # Skip dates before the requested start_date
        while current_date < start_date:
            if self.recurrence_type == 'daily':
                current_date += timedelta(days=self.recurrence_interval)
            elif self.recurrence_type == 'weekly':
                current_date += timedelta(weeks=self.recurrence_interval)
            elif self.recurrence_type == 'monthly':
                # Add months by replacing month/year values
                year_increment = (current_date.month + self.recurrence_interval - 1) // 12
                new_month = ((current_date.month + self.recurrence_interval - 1) % 12) + 1
                current_date = current_date.replace(year=current_date.year + year_increment, month=new_month)
            else:  # yearly
                current_date = current_date.replace(year=current_date.year + self.recurrence_interval)

        # Generate occurrences
        while current_date <= recurrence_end:
            if self.recurrence_type == 'weekly':
                # For weekly recurrence, check if the day is in recurrence_days
                weekday_map = {
                    'monday': 0, 'tuesday': 1, 'wednesday': 2, 
                    'thursday': 3, 'friday': 4, 'saturday': 5, 'sunday': 6
                }
                recurrence_days_list = []
                
                try:
                    # Try to parse as JSON first
                    import json
                    days = json.loads(self.recurrence_days)
                    if isinstance(days, list):
                        for day in days:
                            if day.lower() in weekday_map:
                                recurrence_days_list.append(weekday_map[day.lower()])
                except (json.JSONDecodeError, TypeError):
                    # Fall back to string parsing
                    for day in self.recurrence_days.split(','):
                        day = day.strip().lower()
                        if day in weekday_map:
                            recurrence_days_list.append(weekday_map[day])
                
                if current_date.weekday() in recurrence_days_list:
                    occurrences.append({
                        'date': current_date,
                        'start_time': self.start_time,
                        'end_time': self.end_time
                    })
            else:
                # For daily, monthly (on same day), and yearly (on same date)
                if (self.recurrence_type == 'daily' or 
                    (self.recurrence_type == 'monthly' and current_date.day == self.recurrence_month_day) or
                    (self.recurrence_type == 'yearly' and 
                     current_date.month == self.start_date.month and 
                     current_date.day == self.start_date.day)):
                    occurrences.append({
                        'date': current_date,
                        'start_time': self.start_time,
                        'end_time': self.end_time
                    })

            # Move to next occurrence date
            if self.recurrence_type == 'daily':
                current_date += timedelta(days=self.recurrence_interval)
            elif self.recurrence_type == 'weekly':
                current_date += timedelta(days=1)
                # If we've completed a week, skip to the next interval
                if current_date.weekday() == 0:
                    current_date += timedelta(weeks=self.recurrence_interval - 1)
            elif self.recurrence_type == 'monthly':
                # Add months by replacing month/year values
                year_increment = (current_date.month + self.recurrence_interval - 1) // 12
                new_month = ((current_date.month + self.recurrence_interval - 1) % 12) + 1
                current_date = current_date.replace(year=current_date.year + year_increment, month=new_month)
            else:  # yearly
                current_date = current_date.replace(year=current_date.year + self.recurrence_interval)

        return occurrences

    def __str__(self):
        recurrence_str = f" ({self.get_recurrence_type_display()})" if self.recurrence_type != 'none' else ""
        return f"{self.doctor.get_full_name()} - {self.start_date} {self.start_time}-{self.end_time}{recurrence_str}" 