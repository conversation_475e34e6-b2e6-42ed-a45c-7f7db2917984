from .availability import DoctorAvailabilitySerializer
from .override import DoctorAvailabilityOverrideSlotSerializer, DoctorAvailabilityOverrideSerializer
from .appointment import AppointmentCreateSerializer, AppointmentUpdateSerializer, AppointmentAttachmentSerializer
from .doctor_message import Doctor<PERSON><PERSON><PERSON>InfoSerializer, DoctorMessageSerializer
from .consultation_profile import DoctorC<PERSON>ultationProfileSerializer, DoctorConsultationProfileListSerializer

__all__ = [
    'DoctorAvailabilitySerializer',
    'DoctorAvailabilityOverrideSlotSerializer',
    'DoctorAvailabilityOverrideSerializer',
    'AppointmentCreateSerializer',
    'AppointmentUpdateSerializer',
    'AppointmentAttachmentSerializer',
    'DoctorContactInfoSerializer',
    'DoctorMessageSerializer',
    'DoctorConsultationProfileSerializer',
    'DoctorConsultationProfileListSerializer',
]