from rest_framework import serializers
from django.contrib.auth import get_user_model
from appointments.models import DoctorConsultationProfile
from accounts.models import CustomUser

User = get_user_model()

class DoctorConsultationProfileSerializer(serializers.ModelSerializer):
    """
    Serializer for DoctorConsultationProfile model - for individual doctors only
    """
    consultation_fee_usd = serializers.SerializerMethodField(read_only=True)
    consultation_fee_display = serializers.SerializerMethodField(read_only=True)
    specializations_display = serializers.SerializerMethodField(read_only=True)
    languages_display = serializers.SerializerMethodField(read_only=True)
    is_available_for_telemedicine = serializers.SerializerMethodField(read_only=True)
    
    # Doctor info fields (read-only)
    doctor_name = serializers.CharField(source='user.get_full_name', read_only=True)
    doctor_email = serializers.Char<PERSON>ield(source='user.email', read_only=True)
    
    class Meta:
        model = DoctorConsultationProfile
        fields = [
            'id', 'consultation_fee', 'consultation_duration',
            'accepts_telemedicine', 'bio', 'specializations', 'languages',
            'is_active', 'stripe_account_setup', 'available_hours',
            'created_at', 'updated_at',
            # Computed fields
            'consultation_fee_usd', 'consultation_fee_display',
            'specializations_display', 'languages_display',
            'is_available_for_telemedicine',
            # Doctor info
            'doctor_name', 'doctor_email'
        ]
        read_only_fields = [
            'id', 'created_at', 'updated_at', 'consultation_fee_usd',
            'consultation_fee_display', 'specializations_display',
            'languages_display', 'is_available_for_telemedicine',
            'doctor_name', 'doctor_email'
        ]
        extra_kwargs = {
            'consultation_fee': {
                'help_text': 'Consultation fee in cents (e.g., 5000 = $50.00)',
                'min_value': 500,
                'max_value': 50000
            },
            'consultation_duration': {
                'help_text': 'Consultation duration in minutes',
                'min_value': 15,
                'max_value': 180
            },
            'bio': {
                'max_length': 1000,
                'help_text': "Doctor's professional bio for telemedicine"
            },
            'specializations': {
                'help_text': 'List of medical specializations (JSON array)'
            },
            'languages': {
                'help_text': 'Languages spoken by the doctor (JSON array)'
            }
        }
    
    def get_consultation_fee_usd(self, obj):
        """Get consultation fee in USD format"""
        return obj.get_consultation_fee_usd()
    
    def get_consultation_fee_display(self, obj):
        """Get formatted consultation fee for display"""
        return obj.get_consultation_fee_display()
    
    def get_specializations_display(self, obj):
        """Get comma-separated specializations"""
        return obj.get_specializations_display()
    
    def get_languages_display(self, obj):
        """Get comma-separated languages"""
        return obj.get_languages_display()
    
    def get_is_available_for_telemedicine(self, obj):
        """Check if doctor is available for telemedicine"""
        return obj.is_available_for_telemedicine()
    
    def validate_specializations(self, value):
        """Validate specializations field"""
        if not isinstance(value, list):
            raise serializers.ValidationError("Specializations must be a list")
        
        if len(value) > 10:
            raise serializers.ValidationError("Maximum 10 specializations allowed")
        
        for spec in value:
            if not isinstance(spec, str):
                raise serializers.ValidationError("Each specialization must be a string")
            if len(spec) > 100:
                raise serializers.ValidationError("Each specialization must be less than 100 characters")
        
        return value
    
    def validate_languages(self, value):
        """Validate languages field"""
        if not isinstance(value, list):
            raise serializers.ValidationError("Languages must be a list")
        
        if len(value) > 15:
            raise serializers.ValidationError("Maximum 15 languages allowed")
        
        for lang in value:
            if not isinstance(lang, str):
                raise serializers.ValidationError("Each language must be a string")
            if len(lang) > 50:
                raise serializers.ValidationError("Each language must be less than 50 characters")
        
        return value
    
    def validate(self, attrs):
        """Cross-field validation"""
        # If accepts_telemedicine is True, bio should be provided
        if attrs.get('accepts_telemedicine') and not attrs.get('bio'):
            raise serializers.ValidationError({
                'bio': 'Bio is required when accepting telemedicine appointments'
            })
            
        # TEMPORARILY COMMENTED OUT - Validate user is doctor
        request = self.context.get('request')
        if not request:
            raise serializers.ValidationError("Request context is required")
        
        current_user = request.user
        # if not current_user.role or current_user.role.name != 'doctor':
        #     raise serializers.ValidationError("Only doctors can manage consultation profiles")
        
        # For create operation, check if profile already exists
        if not self.instance:  # Creating new profile
            if DoctorConsultationProfile.objects.filter(user=current_user).exists():
                raise serializers.ValidationError("You already have a consultation profile")
        
        return attrs
    
    def create(self, validated_data):
        """Create consultation profile for current doctor"""
        request = self.context.get('request')
        current_user = request.user
        
        # Create consultation profile for current doctor
        consultation_profile = DoctorConsultationProfile.objects.create(
            user=current_user,
            **validated_data
        )
        
        return consultation_profile
    
    def update(self, instance, validated_data):
        """Update consultation profile"""
        # Update fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        
        instance.save()
        return instance


class DoctorConsultationProfileListSerializer(serializers.ModelSerializer):
    """
    Simplified serializer for listing consultation profiles
    """
    consultation_fee_display = serializers.SerializerMethodField()
    specializations_display = serializers.SerializerMethodField()
    languages_display = serializers.SerializerMethodField()
    is_available_for_telemedicine = serializers.SerializerMethodField()
    doctor_name = serializers.CharField(source='user.get_full_name', read_only=True)
    doctor_email = serializers.CharField(source='user.email', read_only=True)
    
    class Meta:
        model = DoctorConsultationProfile
        fields = [
            'id', 'consultation_fee', 'consultation_duration',
            'accepts_telemedicine', 'is_active', 'stripe_account_setup',
            'consultation_fee_display', 'specializations_display',
            'languages_display', 'is_available_for_telemedicine',
            'doctor_name', 'doctor_email', 'created_at', 'updated_at'
        ]
    
    def get_consultation_fee_display(self, obj):
        return obj.get_consultation_fee_display()
    
    def get_specializations_display(self, obj):
        return obj.get_specializations_display()
    
    def get_languages_display(self, obj):
        return obj.get_languages_display()
    
    def get_is_available_for_telemedicine(self, obj):
        return obj.is_available_for_telemedicine() 