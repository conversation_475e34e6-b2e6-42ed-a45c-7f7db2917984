import logging
from rest_framework import serializers
from appointments.models import Appointment, AppointmentAttachment
from upload.models import UploadedFile
from django.utils import timezone
from django.db import models
from appointments.models import DoctorAvailability

logger = logging.getLogger(__name__)

class AppointmentAttachmentSerializer(serializers.ModelSerializer):
    file_url = serializers.SerializerMethodField()
    filename = serializers.SerializerMethodField()
    
    class Meta:
        model = AppointmentAttachment
        fields = ['id', 'file', 'file_url', 'filename', 'description', 'created_at']
        read_only_fields = ['id', 'file_url', 'filename', 'created_at']
    
    def get_file_url(self, obj):
        return obj.file.file_url
    
    def get_filename(self, obj):
        return obj.file.filename

class AppointmentCreateSerializer(serializers.ModelSerializer):
    doctor_id = serializers.CharField(write_only=True, required=False)
    email = serializers.EmailField(write_only=True, required=False)
    file_ids = serializers.ListField(
        child=serializers.CharField(),
        write_only=True,
        required=False,
        help_text="List of file IDs to attach to the appointment"
    )
    attachments = AppointmentAttachmentSerializer(many=True, read_only=True)
    insurance = serializers.BooleanField(required=False, default=False)
    direct_payment = serializers.BooleanField(required=False, default=False)
    consultation_type = serializers.CharField(write_only=True, required=False, help_text="Consultation service code for future use")

    class Meta:
        model = Appointment
        fields = '__all__'
        read_only_fields = (
            'cancelled_by', 'doctor', 'creator',
            'meeting_link', 'google_event_id'
        )

    def validate(self, data):
        """Validation for creating new appointment"""
        logger.info("=== VALIDATE METHOD CALLED ===")
        request = self.context.get('request')
        # Log request details for debugging
        # logger.info("Validating appointment creation request")
        # logger.info(f"Request Method: {request.method}")
        # logger.info(f"Request User: {request.user}")
        # logger.info(f"Request Data: {request.data}")
        # logger.info(f"Request Query Params: {request.query_params}")
        # logger.info(f"Request Headers: {request.headers}")

        if not request:
            raise serializers.ValidationError({
                'error': 'Request context required',
                'detail': 'Request context is required for appointment creation'
            })

        # Set creator based on authentication status
        if request.user.is_authenticated:
            logger.info(f"Setting creator to authenticated user: {request.user}")
            data['creator'] = request.user
        else:
            email = data.get('email')
            if not email:
                raise serializers.ValidationError({
                    'error': 'Email required',
                    'detail': 'Email is required for unauthenticated users'
                })
            from accounts.models import CustomUser
            user, created = CustomUser.objects.get_or_create(
                email=email,
                defaults={'is_active': True}
            )
            logger.info(f"Created/found user for email {email}: {user}")
            data['creator'] = user

        if not data.get('start_time') or not data.get('end_time'):
            raise serializers.ValidationError({
                'error': 'Missing required fields',
                'detail': 'start_time and end_time are required'
            })

        if data['start_time'] >= data['end_time']:
            raise serializers.ValidationError({
                'error': 'Invalid time range',
                'detail': 'End time must be after start time'
            })

        appointment_type = data.get('appointment_type', 'manual')
        logger.info(f"Processing appointment type: {appointment_type}")
        
        # For booking type appointments, validate that mode matches doctor's availability
        if appointment_type == 'booking':
            doctor_id = data.get('doctor_id')
            if not doctor_id:
                raise serializers.ValidationError({
                    'error': 'Doctor ID required',
                    'detail': 'Doctor ID is required for booking type appointments'
                })

            # Set patient to creator for booking appointments
            logger.info(f"Setting patient to creator: {data['creator']}")
            data['patient'] = data['creator']

            start_time = data['start_time']
            end_time = data['end_time']
            requested_mode = data.get('mode', 'in_person')
            logger.info(f"Requested mode: {requested_mode}")

            # Get all active availabilities for the doctor
            availabilities = DoctorAvailability.objects.filter(
                doctor_id=doctor_id,
                is_active=True
            ).filter(
                models.Q(
                    # Non-recurring availabilities
                    recurrence_type='none',
                    start_date=start_time.date()
                ) |
                models.Q(
                    # Recurring availabilities
                    ~models.Q(recurrence_type='none'),
                    start_date__lte=start_time.date()
                )
            )
            logger.info(f"Found {availabilities.count()} availabilities for doctor {doctor_id}")

            # Check if the slot is available and get its mode
            slot_available = False
            availability_modes = None
            for availability in availabilities:
                occurrences = availability.get_occurrences(
                    start_time.date(),
                    end_time.date()
                )
                
                for occurrence in occurrences:
                    if (occurrence['date'] == start_time.date() and
                        occurrence['start_time'] <= start_time.time() and
                        occurrence['end_time'] >= end_time.time()):
                        slot_available = True
                        availability_modes = availability.mode
                        logger.info(f"Found available slot with modes: {availability_modes}")
                        break
                
                if slot_available:
                    break

            if not slot_available:
                raise serializers.ValidationError({
                    'error': 'Slot not available',
                    'detail': 'The selected time slot is not available'
                })

            # Validate that the requested mode is in the availability modes
            availability_modes = [m.strip() for m in availability.mode.split(',')]
            if requested_mode not in availability_modes:
                raise serializers.ValidationError({
                    'error': 'Mode mismatch',
                    'detail': f'Doctor availability is set to {", ".join(availability_modes)} modes, but appointment was requested in {requested_mode} mode'
                })

            # Set the mode from the request
            data['mode'] = requested_mode

            # Validate insurance/direct_payment mutual exclusion
            insurance = data.get('insurance', False)
            direct_payment = data.get('direct_payment', False)

            if insurance and direct_payment:
                raise serializers.ValidationError({
                    'error': 'Payment method conflict',
                    'detail': 'Appointment cannot use both insurance and direct payment simultaneously. Please choose one payment method.'
                })

            data['insurance'] = bool(insurance)
            data['direct_payment'] = bool(direct_payment)

        elif appointment_type == 'manual':
            # For manual appointments, ensure no doctor is specified
            if data.get('doctor_id'):
                raise serializers.ValidationError({
                    'error': 'Invalid appointment type',
                    'detail': 'Manual appointments cannot have a doctor assigned'
                })

            # For manual appointments, creator must be authenticated
            if not request.user.is_authenticated:
                raise serializers.ValidationError({
                    'error': 'Authentication required',
                    'detail': 'You must be logged in to create manual appointments'
                })

            # For manual, these fields must not be set
            if 'insurance' in data and data['insurance']:
                raise serializers.ValidationError({'insurance': 'Insurance is only allowed for booking appointments.'})
            if 'direct_payment' in data and data['direct_payment']:
                raise serializers.ValidationError({'direct_payment': 'Direct payment is only allowed for booking appointments.'})

        # Handle consultation_type for future telemedicine integration
        consultation_type = data.pop('consultation_type', None)
        if consultation_type and data.get('appointment_type') == 'booking' and data.get('mode') == 'video_call':
            try:
                from content_management.models import Service
                service = Service.objects.get(
                    service_code=consultation_type,
                    service_type='CONSULTATION',
                    is_active=True
                )
                data['consultation_service'] = service
                logger.info(f"Linked consultation service: {service.name}")
            except Service.DoesNotExist:
                logger.warning(f"Consultation service not found for code: {consultation_type}")
                # Don't raise error, just log warning for future compatibility

        logger.info(f"Validation successful. Final data: {data}")
        return data
    
    def create(self, validated_data):
        file_ids = validated_data.pop('file_ids', [])
        appointment = super().create(validated_data)
        
        # Create attachments for each file ID
        for file_id in file_ids:
            try:
                file = UploadedFile.objects.get(id=file_id)
                AppointmentAttachment.objects.create(
                    appointment=appointment,
                    file=file
                )
            except UploadedFile.DoesNotExist:
                # Skip if file doesn't exist
                pass
        
        return appointment

class AppointmentUpdateSerializer(serializers.ModelSerializer):
    file_ids = serializers.ListField(
        child=serializers.CharField(),
        write_only=True,
        required=False,
        help_text="List of file IDs to attach to the appointment"
    )
    attachments = AppointmentAttachmentSerializer(many=True, read_only=True)
    insurance = serializers.BooleanField(required=False, default=False)
    direct_payment = serializers.BooleanField(required=False, default=False)
    
    class Meta:
        model = Appointment
        fields = '__all__'
        read_only_fields = (
            'creator', 'patient', 'doctor',
            'appointment_type', 'slot_reference',
            'start_time', 'end_time',
            'cancelled_by'
        )

    def validate(self, data):
        print("validate data")
        print(data)
        """Validation for updating appointment"""
        appointment = self.instance
        
        # Validate status transitions
        if 'status' in data:
            new_status = data['status']
            current_status = appointment.status
            
            valid_transitions = {
                'pending': ['confirmed', 'canceled'],
                'confirmed': ['completed', 'canceled'],
                'canceled': [],
                'completed': []
            }
            
            if new_status not in valid_transitions[current_status]:
                raise serializers.ValidationError({
                    'error': 'Invalid status transition',
                    'detail': f'Cannot change status from {current_status} to {new_status}'
                })

            # Validate cancellation reason
            if new_status == 'canceled':
                cancellation_reason = data.get('cancellation_reason', '').strip()
                if not cancellation_reason:
                    raise serializers.ValidationError({
                        'cancellation_reason': 'Cancellation reason is required when canceling an appointment'
                    })
                data['cancellation_reason'] = cancellation_reason

        return data

    def update(self, instance, validated_data):
        file_ids = validated_data.pop('file_ids', [])
        appointment = super().update(instance, validated_data)
        
        # Create attachments for each file ID
        for file_id in file_ids:
            try:
                file = UploadedFile.objects.get(id=file_id)
                # Check if attachment already exists
                if not AppointmentAttachment.objects.filter(appointment=appointment, file=file).exists():
                    AppointmentAttachment.objects.create(
                        appointment=appointment,
                        file=file
                    )
            except UploadedFile.DoesNotExist:
                # Skip if file doesn't exist
                pass
        
        return appointment 