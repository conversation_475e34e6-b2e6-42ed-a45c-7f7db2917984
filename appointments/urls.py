from django.urls import include, path
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

from appointments.api.views.appointment import AppointmentViewSet
from appointments.api.views.availability import DoctorAvailabilityViewSet
from appointments.api.views.doctor_message import (
    DoctorContactInfoViewSet, 
    DoctorMessageViewSet,
    VerifyPrimaryEmailView,
    VerifySecondaryEmailView,
    VerifyPrimaryAppointmentEmailView,
    VerifySecondaryAppointmentEmailView
)
from appointments.api.views.override import DoctorAvailabilityOverrideViewSet
from appointments.api.views.google_auth import GoogleAuthView, GoogleAuthCallbackView, GoogleAuthDisconnectView
from appointments.api.views.consultation_profile import DoctorConsultationProfileViewSet

router = DefaultRouter()
router.register(r'doctor-contact-info', DoctorContactInfoViewSet, basename='doctor-contact-info')
router.register(r'doctor-messages', DoctorMessageViewSet, basename='doctor-messages')
router.register(r'availabilities', DoctorAvailabilityViewSet, basename='availabilities')
router.register(r'overrides', DoctorAvailabilityOverrideViewSet, basename='overrides')
router.register(r'doctor-consultation-profiles', DoctorConsultationProfileViewSet, basename='doctor-consultation-profiles')
router.register(r'', AppointmentViewSet, basename='appointments')

urlpatterns = [
    path("", include(router.urls)),
    path("google/auth/", GoogleAuthView.as_view(), name='google-auth'),
    path("google/callback/", GoogleAuthCallbackView.as_view(), name='google-callback'),
    path("google/disconnect/", GoogleAuthDisconnectView.as_view(), name='google-disconnect'),
    path("doctor-contact-info/verify-primary-email/<uuid:token>/", 
         VerifyPrimaryEmailView.as_view(), name='verify-primary-email'),
    path("doctor-contact-info/verify-secondary-email/<uuid:token>/", 
         VerifySecondaryEmailView.as_view(), name='verify-secondary-email'),
    path("doctor-appointment-email/verify-primary-email/<uuid:token>/", 
         VerifyPrimaryAppointmentEmailView.as_view(), name='verify-primary-appointment-email'),
    path("doctor-appointment-email/verify-secondary-email/<uuid:token>/", 
         VerifySecondaryAppointmentEmailView.as_view(), name='verify-secondary-appointment-email'),
]