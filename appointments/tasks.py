from celery import shared_task
import logging
from appointments.models import Appointment
from appointments.services.google_calendar import get_google_calendar_service, sync_to_calendar
from accounts.send_email import send_email

logger = logging.getLogger(__name__)

@shared_task
def sync_appointment_to_google_calendar(appointment_id):
    """
    Create or update Google Calendar event when Appointment is created/updated.
    Syncs to both doctor's and patient's calendars if they have Google Calendar connected.
    """
    try:
        appointment = Appointment.objects.get(id=appointment_id)
        
        # Sync for doctor
        if appointment.doctor:
            doctor_service = get_google_calendar_service(appointment.doctor)
            if doctor_service:
                sync_to_calendar(doctor_service, appointment)
        
        # Sync for patient
        if appointment.patient:
            patient_service = get_google_calendar_service(appointment.patient)
            if patient_service:
                sync_to_calendar(patient_service, appointment)
        
        # Sync to system calendar as backup
        system_service = get_google_calendar_service(None)  # Service account
        if system_service:
            sync_to_calendar(system_service, appointment)

    except Exception as e:
        logger.error(f"Error syncing Google Calendar for appointment {appointment_id}: {str(e)}")
        raise

@shared_task
def delete_google_calendar_event(appointment_id):
    """
    Delete Google Calendar event when Appointment is cancelled.
    Deletes from all connected calendars.
    """
    try:
        appointment = Appointment.objects.get(id=appointment_id)
        if not appointment.google_event_id:
            return

        # Delete from doctor's calendar
        if appointment.doctor:
            doctor_service = get_google_calendar_service(appointment.doctor)
            if doctor_service:
                try:
                    doctor_service.events().delete(
                        calendarId='primary',
                        eventId=appointment.google_event_id
                    ).execute()
                except Exception as e:
                    logger.error(f"Error deleting from doctor's calendar: {str(e)}")

        # Delete from patient's calendar
        if appointment.patient:
            patient_service = get_google_calendar_service(appointment.patient)
            if patient_service:
                try:
                    patient_service.events().delete(
                        calendarId='primary',
                        eventId=appointment.google_event_id
                    ).execute()
                except Exception as e:
                    logger.error(f"Error deleting from patient's calendar: {str(e)}")

        # Delete from system calendar
        system_service = get_google_calendar_service(None)
        if system_service:
            try:
                system_service.events().delete(
                    calendarId='primary',
                    eventId=appointment.google_event_id
                ).execute()
            except Exception as e:
                logger.error(f"Error deleting from system calendar: {str(e)}")

        # Clear google_event_id
        appointment.google_event_id = None
        appointment.save()

    except Exception as e:
        logger.error(f"Error deleting Google Calendar event for appointment {appointment_id}: {str(e)}")
        raise

@shared_task
def send_appointment_reminder(appointment_id):
    """
    Send reminder 24 hours before the appointment.
    """
    try:
        appointment = Appointment.objects.get(id=appointment_id)
        if appointment.appointment_type == 'booking':
            subject = "Appointment Reminder"
            message = f"Reminder: You have an appointment with {appointment.doctor.get_full_name()} on {appointment.start_time}."
            if appointment.mode == 'video_call' and appointment.meeting_link:
                message += f"\n\nJoin the video call at: {appointment.meeting_link}"
            send_email(appointment.patient.email, subject, message)
            logger.info(f"Sent reminder for appointment {appointment_id}")
    except Exception as e:
        logger.error(f"Error sending reminder for appointment {appointment_id}: {str(e)}")
        raise