from rest_framework import serializers

from upload.views import FileUploadView
from .constant import MAXIMUM_FILE_COUNT, MAXIMUM_FILE_SIZE, MAXIMUM_TOTAL_FILE_SIZE, ALLOWED_TYPES, MAXIMUM_FILE_SIZE_MB, MAXIMUM_TOTAL_FILE_SIZE_MB

file_upload_view = FileUploadView()

def validate_files(files):
    # Check number of files in current request
    # TODO: dynamic max file count based on user tier
    if len(files) > MAXIMUM_FILE_COUNT:
        return {"error": f"Maximum {MAXIMUM_FILE_COUNT} files allowed per upload"}
    
    if len(files) == 0:
        return {"error": "No files uploaded"}

    total_size = 0
    allowed_types = ALLOWED_TYPES

    for file in files:
        # Check individual file size (2MB limit)
        if file.size > MAXIMUM_FILE_SIZE:  # 2MB in bytes
            return {"error": f"File {file.name} exceeds {MAXIMUM_FILE_SIZE_MB}MB limit"}

        # Validate file type
        if file.content_type not in allowed_types:
            return {"error": f"Invalid file type for {file.name}. Allowed types: PDF, DOC, PNG, JPG, JPEG, MOV"}

        total_size += file.size

    # Check total upload size
    # TODO: dynamic max total file size based on user tier
    if total_size > MAXIMUM_TOTAL_FILE_SIZE:  # 10MB in bytes
        return {"error": f"Free tier limit: Total upload size cannot exceed {MAXIMUM_TOTAL_FILE_SIZE_MB}MB"}

    return files

def get_file_data(obj):
        if not obj.file:
            return None
        return {
            'id': obj.file.id,
            'name': obj.file.filename,
            'url': file_upload_view.get_signed_url(str(obj.file.file)),
            'type': obj.file.file_type
        }