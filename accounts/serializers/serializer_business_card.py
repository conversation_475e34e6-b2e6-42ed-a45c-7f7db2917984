from rest_framework import serializers
from accounts.models import BusinessCard
from roles.gcp_utils import get_private_profile_picture_signed_url, get_profile_picture_signed_url

class BusinessCardSerializer(serializers.ModelSerializer):
    # private_profile_picture = serializers.SerializerMethodField()
    profile_picture = serializers.SerializerMethodField()

    class Meta:
        model = BusinessCard
        fields = [
            'id', 'user', 'first_name', 'middle_name', 'last_name',
            'professional_title', 'address', 'phone', 'email',
            'additional_information', 'url', 'profile_picture'
        ]
        read_only_fields = ['id', 'user']

    # def get_private_profile_picture(self, obj):
    #     user = obj.user
    #     profile = getattr(user, 'profile', None)
    #     if profile and profile.private_profile_picture:
    #         return get_private_profile_picture_signed_url(profile.private_profile_picture, user.id)
    #     return None
    def get_profile_picture(self, obj):
        user = obj.user
        profile = getattr(user, 'profile', None)
        if profile and profile.profile_picture:
            return get_profile_picture_signed_url(profile.profile_picture, user.id)
        return None

class QRCodeSerializer(serializers.Serializer):
    qr_code = serializers.CharField(required=False, allow_blank=True)
    # Extend this if you want to store QR as image/base64

    def to_representation(self, instance):
        # Example: generate QR code from user info or return stored QR
        # Here, just return a placeholder or a field from profile
        return {
            'qr_code': getattr(instance.profile, 'qr_code', '') if hasattr(instance, 'profile') else ''
        }

    def update(self, instance, validated_data):
        qr_code = validated_data.get('qr_code', None)
        if hasattr(instance, 'profile') and qr_code is not None:
            instance.profile.qr_code = qr_code
            instance.profile.save()
        return instance 